<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-level Product Tree</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .tree-container {
            max-width: 400px;
            margin: 20px auto;
        }
        .tree-item {
            cursor: pointer;
            padding: 8px;
            margin: 5px 0;
            background-color: #f4f4f4;
            border-radius: 5px;
            transition: all 0.3s;
            text-align: center;
        }
        .tree-item:hover {
            background-color: #ddd;
        }
        .children {
            margin-left: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>

    <div class="tree-container">
        <h2>Product Tree</h2>
        <div id="product-tree"></div>
    </div>

    <script>
        // Dữ liệu Collections
        const collections = {
            CollectionA: {
                A: ["B", "C"],
                B: ["D", "E", "F"],
                C: ["G", "H"],
                D: ["I"],
                E: ["J", "K"],
                F: ["L"],
                G: ["M"],
                H: ["N", "O"],
                J: ["P"],
                K: ["Q"],
                N: ["R"],
                O: ["S"],
                Q: ["T"],
                T: ["U"],
                U: ["V"],
                V: ["W"],
                W: ["X"],
                X: ["Y"],
                Y: ["Z"]
            }
        };

        // Biến lưu trạng thái sản phẩm đang được chọn
        let currentParent = null;

        // Hàm render danh sách sản phẩm con
        function renderChildren(parentElement, collection, product) {
            parentElement.innerHTML = ""; // Xóa nội dung cũ
            if (!collections[collection][product]) return;

            collections[collection][product].forEach(child => {
                let item = document.createElement("div");
                item.classList.add("tree-item");
                item.textContent = child;
                item.dataset.product = child;
                item.dataset.collection = collection;

                item.addEventListener("click", function (event) {
                    event.stopPropagation();
                    currentParent = child; // Cập nhật sản phẩm cha mới
                    renderChildren(parentElement, collection, child);
                });

                parentElement.appendChild(item);
            });
        }

        // Hàm render collection chính
        function renderCollection() {
            let treeContainer = document.getElementById("product-tree");
            treeContainer.innerHTML = ""; // Xóa nội dung cũ

            // Hiển thị CollectionA
            let collectionTitle = document.createElement("h3");
            collectionTitle.textContent = "CollectionA";
            treeContainer.appendChild(collectionTitle);

            let rootItem = document.createElement("div");
            rootItem.classList.add("tree-item");
            rootItem.textContent = "A";
            rootItem.dataset.product = "A";
            rootItem.dataset.collection = "CollectionA";

            let childrenContainer = document.createElement("div");
            childrenContainer.classList.add("children");

            rootItem.addEventListener("click", function () {
                currentParent = "A"; // Gán sản phẩm cha là A
                renderChildren(childrenContainer, "CollectionA", "A");
            });

            treeContainer.appendChild(rootItem);
            treeContainer.appendChild(childrenContainer);
        }

        // Khởi chạy ứng dụng
        renderCollection();
    </script>

</body>
</html>
