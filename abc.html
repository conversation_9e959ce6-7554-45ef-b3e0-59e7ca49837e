<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <title>新規会員登録</title>
  <style>
    body {
      font-family: sans-serif;
      background: #fff;
      color: #333;
      display: flex;
      justify-content: center;
    }

    .container {
      width: 700px;
      margin-top: 40px;
    }

    h1 {
      color: #00bcd4;
      text-align: center;
      margin-bottom: 40px;
    }

    form {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }

    label {
      width: 100%;
      font-size: 16px;
    }

    label span {
      color: red;
      font-size: 14px;
      margin-left: 4px;
    }

    input[type="text"],
    input[type="email"],
    input[type="password"],
    select,
    input[type="date"] {
      width: 100%;
      max-width: 600px;
      padding: 10px;
      font-size: 16px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    .row {
      width: 48%;
    }

    .full {
      width: 100%;
    }

    .radio-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-top: 10px;
    }

    .radio-option {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .select-wrapper {
      position: relative;
    }

    select {
      appearance: none;
      background-color: #fff;
      background-position: right 10px center;
      background-repeat: no-repeat;
    }

  </style>
</head>
<body>
  <div class="container">
    <h1>新規会員登録</h1>
    <form>
      <label>お名前 <span>（必須）</span></label>
      <div style="display: flex; gap: 10px;" class="full">
        <input type="text" placeholder="姓" class="row">
        <input type="text" placeholder="名" class="row">
      </div>

      <label>都道府県 <span>（必須）</span></label>
      <select class="full">
        <option>都道府県を選択</option>
      </select>

      <label>メールアドレス <span>（必須）</span></label>
      <input type="email" placeholder="<EMAIL>" class="full">

      <label>パスワード <span>（必須）</span></label>
      <input type="password" placeholder="入力してください" class="full">

      <label>ご利用目的 <span>（必須）</span></label>
      <select class="full">
        <option>ご利用目的を選択</option>
      </select>

      <label>ご利用予定日/挙式予定日 <span>（必須）</span></label>
      <div class="radio-group">
        <div class="radio-option">
          <input type="radio" name="date" checked>
          <label>カレンダーから選ぶ</label>
        </div>
        <input type="date" class="full">

        <div class="radio-option">
          <input type="radio" name="date">
          <label>時期から選ぶ</label>
        </div>

        <div class="radio-option">
          <input type="radio" name="date">
          <label>ご利用予定日を選択</label>
        </div>

        <div class="radio-option">
          <input type="radio" name="date">
          <label>まだ決まっていない</label>
        </div>
      </div>
    </form>
  </div>
</body>
</html>
