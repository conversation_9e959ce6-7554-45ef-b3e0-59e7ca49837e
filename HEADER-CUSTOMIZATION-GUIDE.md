# Datepicker Header Customization Guide

Hướng dẫn chi tiết cách tùy chỉnh header (tiêu đề) của Flatpickr với Flowbite theme để hiển thị tháng và năm theo nhiều cách khác nhau.

## 🎯 Các Kiểu Header Có Sẵn

### 1. Enhanced Default Header
Header mặc định được cải tiến với dropdown tháng và input năm được styling đẹp hơn.

```javascript
flatpickr("#datepicker", {
    dateFormat: "Y-m-d",
    monthSelectorType: 'dropdown', // Cho phép chọn tháng bằng dropdown
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
    }
});
```

**Đặc điểm:**
- ✅ Dropdown tháng với styling Flowbite
- ✅ Input năm có border và hover effects
- ✅ Responsive và accessible
- ✅ Hỗ trợ dark mode

### 2. Custom Gradient Header
Header với background gradient đẹp mắt hiển thị tên tháng đầy đủ và năm.

```javascript
flatpickr("#datepicker", {
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
        
        // Tạo custom header
        const customHeader = document.createElement('div');
        customHeader.className = 'flatpickr-custom-header';
        
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        const updateHeader = () => {
            const monthName = monthNames[instance.currentMonth];
            const year = instance.currentYear;
            customHeader.textContent = `${monthName} ${year}`;
        };
        
        updateHeader();
        instance.calendarContainer.insertBefore(customHeader, instance.calendarContainer.firstChild);
        
        // Cập nhật khi thay đổi tháng/năm
        instance.config.onMonthChange.push(updateHeader);
        instance.config.onYearChange.push(updateHeader);
    }
});
```

**Đặc điểm:**
- 🎨 Background gradient xanh đẹp mắt
- 📝 Hiển thị tên tháng đầy đủ (January, February...)
- 🔄 Tự động cập nhật khi chuyển tháng/năm
- 💫 Animation fade-in mượt mà

### 3. Minimal Text Header
Header đơn giản chỉ có text, phù hợp cho giao diện tối giản.

```javascript
flatpickr("#datepicker", {
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
        
        // Ẩn header mặc định
        const monthsContainer = instance.monthNav;
        if (monthsContainer) {
            monthsContainer.style.display = 'none';
        }
        
        // Tạo text header
        const textHeader = document.createElement('div');
        textHeader.className = 'flatpickr-minimal-header';
        
        const monthNamesShort = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        const updateTextHeader = () => {
            const monthName = monthNamesShort[instance.currentMonth];
            const year = instance.currentYear;
            textHeader.textContent = `${monthName} ${year}`;
        };
        
        updateTextHeader();
        instance.calendarContainer.insertBefore(textHeader, instance.daysContainer.parentNode);
        
        instance.config.onMonthChange.push(updateTextHeader);
        instance.config.onYearChange.push(updateTextHeader);
    }
});
```

**Đặc điểm:**
- 🎯 Thiết kế tối giản, sạch sẽ
- 📅 Tên tháng viết tắt (Jan, Feb...)
- 📏 Border bottom tinh tế
- 🌙 Hỗ trợ dark mode

### 4. Badge Style Header
Header với tháng và năm hiển thị dưới dạng badges (nhãn) riêng biệt.

```javascript
flatpickr("#datepicker", {
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
        
        // Ẩn header mặc định
        const monthsContainer = instance.monthNav;
        if (monthsContainer) {
            monthsContainer.style.display = 'none';
        }
        
        // Tạo badge header
        const badgeHeader = document.createElement('div');
        badgeHeader.className = 'flatpickr-badge-header';
        
        const monthBadge = document.createElement('span');
        monthBadge.className = 'flatpickr-month-badge';
        
        const yearBadge = document.createElement('span');
        yearBadge.className = 'flatpickr-year-badge';
        
        const updateBadgeHeader = () => {
            const monthNamesShort = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            monthBadge.textContent = monthNamesShort[instance.currentMonth];
            yearBadge.textContent = instance.currentYear;
        };
        
        updateBadgeHeader();
        badgeHeader.appendChild(monthBadge);
        badgeHeader.appendChild(yearBadge);
        instance.calendarContainer.insertBefore(badgeHeader, instance.daysContainer.parentNode);
        
        instance.config.onMonthChange.push(updateBadgeHeader);
        instance.config.onYearChange.push(updateBadgeHeader);
    }
});
```

**Đặc điểm:**
- 🏷️ Tháng và năm là badges riêng biệt
- 🎨 Tháng có màu xanh primary, năm có màu xám
- 📱 Responsive - chuyển thành cột trên mobile
- ✨ Rounded corners đẹp mắt

## 🎨 CSS Classes Có Sẵn

### Header Classes
```css
.flatpickr-custom-header      /* Gradient header */
.flatpickr-minimal-header     /* Text-only header */
.flatpickr-badge-header       /* Badge container */
.flatpickr-month-badge        /* Month badge */
.flatpickr-year-badge         /* Year badge */
.flatpickr-icon-header        /* Header with icons */
.flatpickr-compact-header     /* Compact style header */
```

### Utility Classes
```css
.custom-header                /* Hide default header */
```

## 🛠️ Tùy Chỉnh Nâng Cao

### Thêm Icon Vào Header
```javascript
// Tạo header với icon
const iconHeader = document.createElement('div');
iconHeader.className = 'flatpickr-icon-header';
iconHeader.innerHTML = `
    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
        <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1z"/>
    </svg>
    <span>January 2024</span>
`;
```

### Header Với Animation
```css
.flatpickr-custom-header {
    animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
```

### Responsive Header
```css
@media (max-width: 640px) {
    .flatpickr-badge-header {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .flatpickr-custom-header {
        font-size: 1rem;
        padding: 0.5rem;
    }
}
```

## 📱 Demo Files

- `flowbite-header-demo.html` - Demo tất cả các kiểu header
- `flowbite-flatpickr-demo.html` - Demo tổng hợp với nhiều tính năng
- `flatpickr-themes-demo.html` - So sánh với các theme khác

## 🎯 Tips & Best Practices

1. **Luôn thêm class `flowbite-theme`** vào calendar container
2. **Sử dụng `onMonthChange` và `onYearChange`** để cập nhật header
3. **Ẩn header mặc định** khi sử dụng custom header
4. **Test trên mobile** để đảm bảo responsive
5. **Hỗ trợ dark mode** bằng cách thêm CSS cho `.dark`

## 🔧 Troubleshooting

**Q: Header không hiển thị?**
A: Đảm bảo đã thêm class `flowbite-theme` và CSS file được load đúng.

**Q: Header không cập nhật khi chuyển tháng?**
A: Thêm callback functions vào `onMonthChange` và `onYearChange`.

**Q: Dark mode không hoạt động?**
A: Kiểm tra class `dark` đã được thêm vào `<html>` element chưa.

**Q: Header bị lỗi trên mobile?**
A: Sử dụng CSS responsive và test trên các kích thước màn hình khác nhau.
