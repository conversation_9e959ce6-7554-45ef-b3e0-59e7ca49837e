{"name": "react-datepicker-library", "version": "1.0.0", "description": "A comprehensive React datepicker library with time slot support", "main": "DatePicker.jsx", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@heroicons/react": "^2.0.18"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8"}, "keywords": ["react", "datepicker", "calendar", "time-slots", "tailwindcss", "component", "ui"], "author": "Your Name", "license": "MIT"}