<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Delivery Cart Test</title>
</head>
<body>
  <delivery-cart>
    <product-delivery-date
      data-company-shipping-name="CtyA"
      data-delivery-unit-ref-day="3"
      data-delivery-category-ref-day="3"
      data-order-date="2025-05-09"
    >
      <li>Ahihi</li>
    </product-delivery-date>
    
    <div id="delivery-date"></div>
  </delivery-cart>

  <script>
    class DeliveryCart extends HTMLElement {
      productDeliveryDatesLastest = null;
      _deliveryCartDate = null;

      constructor() {
        super();
      }

      get deliveryCartDate() {
        return this._deliveryCartDate;
      }

      set deliveryCartDate(value) {
        this._deliveryCartDate = value;
        this.updateAttributeDeliveryCartDate(value);
      }

      getIsMultipleDeliveryUnit() {
        const deliveryUnitNames = [];
        this.querySelectorAll('product-delivery-date').forEach(productDeliveryDate => {
          const deliveryUnitName = productDeliveryDate.getAttribute('data-company-shipping-name');
          if (!deliveryUnitNames.includes(deliveryUnitName)) {
            deliveryUnitNames.push(deliveryUnitName);
          }
        });
        return deliveryUnitNames.length > 1;
      }

      connectedCallback() {
        requestAnimationFrame(() => {
          const deliveryDateElement = this.querySelector('#delivery-date');
          const orderDateLastest = [];

          this.querySelectorAll('product-delivery-date').forEach(productDeliveryDate => {
            orderDateLastest.push(productDeliveryDate.orderDateLastest);
          });

          if (this.getIsMultipleDeliveryUnit()) {
            if (deliveryDateElement) {
              deliveryDateElement.innerHTML = `<p>There are multiple delivery companies available. Please select a delivery provider again.</p>`;
            }
            this.deliveryCartDate = null;
          } else {
            this.productDeliveryDatesLastest = Math.max(...orderDateLastest);
            const deliveryDate = new Date(this.productDeliveryDatesLastest);
            if (deliveryDateElement) {
              deliveryDateElement.innerHTML = `<p>Estimated delivery date: ${deliveryDate.toDateString()}</p>`;
            }
            this.deliveryCartDate = this.productDeliveryDatesLastest;
          }
        });
      }

      async updateAttributeDeliveryCartDate(delivery_cart_date) {
        try {
          // Dummy function for now
          console.log(delivery_cart_date,'delivery_cart_date');
          
        } catch (error) {
          console.error('Error updating delivery cart date:', error);
        }
      }
    }

    customElements.define('delivery-cart', DeliveryCart);

    class ProductDeliveryDate extends HTMLElement {
      orderDateLastest = null;

      constructor() {
        super();
      }

      addBusinessDays(startDate, daysToAdd, holidays = []) {
        let count = 0;
        let currentDate = new Date(startDate);
        while (count < daysToAdd) {
            currentDate.setDate(currentDate.getDate() + 1);

            const dayOfWeek = currentDate.getDay();
            const formatted = currentDate.toISOString().slice(0, 10);

            const isWeekend = (dayOfWeek === 0 || dayOfWeek === 6);
            const isHoliday = holidays.includes(formatted);

            if (!isWeekend && !isHoliday) {
                count++;
            }
        }
        return currentDate.toISOString().slice(0, 10);
      }

      calculateDeliveryUnitDate() {
        const deliveryUnitRefDay = this.getAttribute('data-delivery-unit-ref-day') || '0';
        let orderDate = this.getAttribute('data-order-date');
        const deliveryDate = this.addBusinessDays(new Date(orderDate), Number(deliveryUnitRefDay), ['2025-05-12']);
        return deliveryDate;
      }

      getLastestDeliveryDate(){
        const deliveryCategoryRefDay = this.getAttribute('data-delivery-category-ref-day') || '0';
        const date = new Date(this.calculateDeliveryUnitDate());
        date.setDate(date.getDate() + Number(deliveryCategoryRefDay));
        return date;
    }

      connectedCallback() {
        this.orderDateLastest = this.getLastestDeliveryDate();
      }
    }

    customElements.define('product-delivery-date', ProductDeliveryDate);
  </script>
</body>
</html>
