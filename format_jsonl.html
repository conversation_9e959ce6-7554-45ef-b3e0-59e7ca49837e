<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSONL to Excel</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <button id="export-btn">Export to Excel</button>
    <script>
        async function fetchJSONL() {
            try {
                // const response = await fetch('all_product.jsonl');
                const response = await fetch("https://storage.googleapis.com/shopify-tiers-assets-prod-us-east1/bulk-operation-outputs/ye14hay7oclmdnmm364kxccr8wtp-final?GoogleAccessId=assets-us-prod%40shopify-tiers.iam.gserviceaccount.com&Expires=**********&Signature=ZgwFEGGVvCemHH%2FyVFQAwKqOrCnxTABq5sPFvxMVZE3MauxC619HVtCf9%2BKsSyTTC0%2FAX1ujwQ00dpLmf4vpqxNeiSU%2FAVFKbT6WXjp%2FPP3l9oiEiYEQVabc8jIBChWtYSb%2FK9KRuQCDlmzmIzpFcODrrlGBKpwn6TnDHOaqj0%2BCgSv7O%2FfV%2BEHbnjQDcseHislJBmn%2Fcs%2FuyqzoabbWiBUSFVbsZO5bdLrHDr0HX7rcwua5LbzWZx9PEKIngIBXOK7vJIPwh2%2BdwzKu35BW7nnhF47YHZJsdiYIHAyqYQSEyfzA8HANj8aUDBZJmfekiPjpRRp0HgoBYAjgyYXiMQ%3D%3D&response-content-disposition=attachment%3B+filename%3D%22bulk-*************.jsonl%22%3B+filename%2A%3DUTF-8%27%27bulk-*************.jsonl&response-content-type=application%2Fjsonl");
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                const lines = text.split('\n').filter(line => line.trim() !== ''); // Remove blank lines
                const data = [];
                lines.forEach((line, index) => {
                    try {
                        const lineJson = JSON.parse(line);
                        const profileId = lineJson.id.split("profile_id=")[1]; // Extract the profile_id
                        const productId = lineJson.product.id.split("/").pop(); // Extract the product ID
                        let name;

                        if (profileId === "123938373912") {
                            name = "パートナー商品配送料（200円）";
                        } else if (profileId === "124215361816") {
                            name = "パートナー商品配送料（400円）";
                        } else if (profileId === "124215394584") {
                            name = "パートナー商品配送料（600円）";
                        } else if (profileId === "124215427352") {
                            name = "パートナー商品配送料（800円）";
                        } else if (profileId === "124215460120") {
                            name = "パートナー商品配送料（1000円）";
                        } else if (profileId === "124215492888") {
                            name = "パートナー商品配送料（1400円）";
                        } else if (profileId === "124849520920") {
                            name = "パートナー商品配送料（1800円）";
                        } else if (profileId === "124850012440") {
                            name = "パートナー商品配送料（2200円）";
                        } else if (profileId === "125196599576") {
                            name = "パートナー商品配送料（5500円）";
                        }

                        data.push({
                            Name: name,
                            ProductID: productId,
                        });
                    } catch (parseError) {
                        console.error(`Error parsing line ${index + 1}:`, parseError.message);
                    }
                });

                return data;

            } catch (error) {
                console.error('Error fetching or parsing JSONL:', error);
                return [];
            }
        }

        async function exportToExcel() {
            const data = await fetchJSONL();

            if (data.length === 0) {
                alert("No data available to export!");
                return;
            }

            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Products");

            // Export to Excel
            XLSX.writeFile(workbook, "Products.xlsx");
        }

        document.getElementById('export-btn').addEventListener('click', exportToExcel);
    </script>
</body>
</html>
