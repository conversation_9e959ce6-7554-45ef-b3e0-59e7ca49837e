<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Layout</title>
    <style>
        /* Basic Reset/Normalization */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: sans-serif; /* Or your preferred font */
            line-height: 1.6;
            background-color: #f0f2f5; /* Example background */
            color: #333; /* Default text color */
        }

        .container {
            width: 90%;
            max-width: 1200px; /* Or your desired max width */
            margin: 0 auto; /* Center the container */
        }

        /* Header Styling */
        .site-header {
            background-color: #fff; /* Example background */
            padding: 10px 0;
            border-bottom: 1px solid #ddd;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo img {
            height: 40px; /* Adjust logo size as needed */
        }

        .header-icons span {
            margin-left: 15px;
            font-size: 1.2em; /* Example size */
            /* Add icon styles here - these are just placeholders */
        }

        .auth-buttons .btn {
            margin-left: 10px;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
        }

        .btn.login {
            background-color: #007bff; /* Example color */
            color: white;
        }

        .btn.register {
            background-color: #28a745; /* Example color */
            color: white;
        }

        .btn.play-now {
             background-color: #ffc107; /* Example color */
             color: #333;
             font-weight: bold;
        }


        /* Navigation Styling */
        .main-nav {
            background-color: #0056b3; /* Example darker blue */
            padding: 10px 0;
        }

        .nav-list {
            display: flex;
            justify-content: center; /* Center the nav items */
            flex-wrap: wrap; /* Allow items to wrap on smaller screens */
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 5px 15px;
            margin: 0 10px;
            transition: color 0.3s ease;
            font-size: 1em;
        }

        .nav-link:hover,
        .nav-link.active {
            color: #ffc107; /* Example hover/active color */
        }

        /* Hero Section Styling */
        .site-main {
            padding: 20px 0;
        }

        .hero-section {
            display: flex;
            align-items: center;
            gap: 20px; /* Space between image and text */
            flex-wrap: wrap; /* Allow sections to stack on smaller screens */
        }

        .hero-image,
        .hero-text {
            flex: 1; /* Allow sections to grow */
            min-width: 300px; /* Minimum width before wrapping */
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
            display: block; /* Remove extra space below image */
        }

        .hero-text h2 {
            color: #0056b3; /* Example text color */
            margin-bottom: 10px;
            font-size: 2em; /* Adjust as needed */
        }

        .email-signup input[type="email"] {
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-top: 15px;
            width: 100%; /* Make input responsive */
            max-width: 300px; /* Max width for the input */
            display: block; /* Input on its own line */
        }


        /* News Ticker Styling */
        .news-ticker {
            background-color: #fff;
            padding: 10px;
            margin-top: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap; /* Allow items to wrap */
        }

        .news-icon {
            width: 20px;
            height: 20px;
            background-color: #007bff; /* Placeholder color */
            flex-shrink: 0; /* Prevent icon from shrinking */
            /* Add styling for your news icon */
        }

        .ticker-text {
            flex-grow: 1; /* Allow text to take up available space */
            /* white-space: nowrap; */ /* Removed nowrap to allow wrapping in single file */
            /* overflow: hidden; */ /* Removed for simplicity in single file */
            /* text-overflow: ellipsis; */ /* Removed for simplicity in single file */
        }

        .news-ticker .btn.view-all {
            background-color: #eee; /* Example color */
            padding: 5px 10px;
            font-size: 0.9em;
            flex-shrink: 0; /* Prevent button from shrinking */
        }

        /* Basic Responsive Adjustment */
        @media (max-width: 768px) {
            .header-content,
            .hero-section,
            .news-ticker {
                flex-direction: column;
                text-align: center;
            }

            .header-icons {
                 margin-top: 10px;
            }

            .auth-buttons {
                margin-top: 10px;
            }

             .auth-buttons .btn {
                 margin: 5px; /* Adjust margin for stacking */
             }

            .nav-list {
                flex-direction: column;
                 align-items: center;
            }

            .nav-link {
                margin: 5px 0; /* Adjust margin for stacking */
            }

            .hero-image,
            .hero-text {
                min-width: 100%; /* Allow sections to take full width */
            }

            .email-signup input[type="email"] {
                 max-width: 100%; /* Input takes full width */
             }

             .ticker-text {
                 text-align: left; /* Align text left */
             }
        }

    </style>
</head>
<body>

    <header class="site-header">
        <div class="container header-content">
            <div class="logo">
                <img src="placeholder-logo.png" alt="F8BET Logo">
                 <p>THỬ NGAY VẬN MAY SẼ ĐẾN</p> </div>
            <div class="header-icons">
                <span>🏠</span> <span>📞</span> <span>🤝</span> <span>👑</span> </div>
            <div class="auth-buttons">
                <button class="btn login">Đăng Nhập</button>
                <button class="btn register">Đăng Ký</button>
                <button class="btn play-now">🎮 Chơi Thử</button>
            </div>
             <div class="header-datetime">2025/05/12 (T2) 10:45:13</div>
        </div>
    </header>

    <nav class="main-nav">
        <div class="container nav-list">
            <a href="#" class="nav-link active">Trang Chủ</a>
            <a href="#" class="nav-link">Casino</a>
            <a href="#" class="nav-link">Nổ Hũ</a>
            <a href="#" class="nav-link">Bắn Cá</a>
            <a href="#" class="nav-link">Thể Thao</a>
            <a href="#" class="nav-link">Game Bài 3D</a>
            <a href="#" class="nav-link">Live Game</a>
            <a href="#" class="nav-link">Đá Gà</a>
            <a href="#" class="nav-link">Xổ Số</a>
        </div>
    </nav>

    <main class="site-main">
        <div class="container hero-section">
            <div class="hero-image">
                <img src="placeholder-banner.png" alt="Promotional Banner">
            </div>
            <div class="hero-text">
                <h2>XEM LIVE CỰC PHÊ</h2>
                <h2>QUÀ VỀ CỰC MÊ</h2>
                 <div class="email-signup">
                    <input type="email" placeholder="<EMAIL>">
                    </div>
            </div>
        </div>

        <div class="container news-ticker">
            <div class="news-icon">📰</div> <p class="ticker-text">Tin Tức Mới Nhất: lấy link ngay https://t.me/f8betcodefree_bot giới thiệu cho bạn bè, người thân để cùng nhau tận hưởng quà tặng h...</p>
            <button class="btn view-all">Tất Cả</button>
        </div>
    </main>

    </body>
</html>