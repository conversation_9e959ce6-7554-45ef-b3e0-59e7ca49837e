# Flowbite Theme for Flatpickr

A beautiful datepicker theme for Flatpickr using Flowbite design system and Tailwind CSS classes.

## Features

- ✅ **Tailwind CSS Integration** - Built using Flowbite's Tailwind utility classes
- ✅ **Dark Mode Support** - Automatic dark mode with proper contrast ratios
- ✅ **Responsive Design** - Works perfectly on all device sizes
- ✅ **Modern Styling** - Clean, professional appearance following Flowbite design principles
- ✅ **Accessibility** - Proper focus states and keyboard navigation
- ✅ **Range Picker Support** - Beautiful styling for date range selection
- ✅ **Action Buttons** - Styled Today and Clear buttons
- ✅ **Smooth Animations** - Subtle transitions and hover effects

## Installation

### 1. Include Required Dependencies

```html
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com"></script>

<!-- Flatpickr CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

<!-- Flowbite Flatpickr Theme -->
<link rel="stylesheet" href="./flatpickr-flowbite.css">

<!-- Flatpickr JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
```

### 2. HTML Structure

```html
<div class="flowbite-datepicker-container">
    <div class="flowbite-datepicker-icon">
        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
        </svg>
    </div>
    <input type="text" id="datepicker" class="flowbite-datepicker-input" placeholder="Select date">
</div>
```

### 3. JavaScript Initialization

```javascript
flatpickr("#datepicker", {
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
    }
});
```

## Usage Examples

### Basic Datepicker

```javascript
flatpickr("#basic-datepicker", {
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
    }
});
```

### With Action Buttons

```javascript
flatpickr("#buttons-datepicker", {
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
        
        // Add custom buttons
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'flatpickr-time';
        
        const todayBtn = document.createElement('button');
        todayBtn.textContent = 'Today';
        todayBtn.className = 'flatpickr-today-button';
        todayBtn.onclick = () => instance.setDate(new Date());
        
        const clearBtn = document.createElement('button');
        clearBtn.textContent = 'Clear';
        clearBtn.className = 'flatpickr-clear-button';
        clearBtn.onclick = () => instance.clear();
        
        buttonContainer.appendChild(todayBtn);
        buttonContainer.appendChild(clearBtn);
        instance.calendarContainer.appendChild(buttonContainer);
    }
});
```

### Date Range Picker

```javascript
const rangeStart = flatpickr("#range-start", {
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
    },
    onChange: function(selectedDates, dateStr, instance) {
        rangeEnd.set('minDate', dateStr);
    }
});

const rangeEnd = flatpickr("#range-end", {
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
    },
    onChange: function(selectedDates, dateStr, instance) {
        rangeStart.set('maxDate', dateStr);
    }
});
```

### Inline Datepicker

```javascript
flatpickr("#inline-datepicker", {
    inline: true,
    dateFormat: "Y-m-d",
    onReady: function(selectedDates, dateStr, instance) {
        instance.calendarContainer.classList.add('flowbite-theme');
    }
});
```

## Dark Mode

The theme automatically supports dark mode when using Tailwind's dark mode classes. Add the `dark` class to your HTML element:

```html
<html class="dark">
```

Or toggle programmatically:

```javascript
// Enable dark mode
document.documentElement.classList.add('dark');

// Disable dark mode
document.documentElement.classList.remove('dark');
```

## CSS Classes

### Input Classes
- `.flowbite-datepicker-input` - Main input field styling
- `.flowbite-datepicker-container` - Container with relative positioning
- `.flowbite-datepicker-icon` - Calendar icon positioning

### Calendar Classes
- `.flowbite-theme` - Main theme class for the calendar
- `.flatpickr-today-button` - Today button styling
- `.flatpickr-clear-button` - Clear button styling

## Customization

You can customize the theme by modifying the CSS variables in `flatpickr-flowbite.css`:

```css
:root {
  --flowbite-primary: #3b82f6;
  --flowbite-primary-hover: #2563eb;
  --flowbite-gray-50: #f9fafb;
  /* ... other variables */
}
```

## Demo

Check out the demo files:
- `flowbite-flatpickr-demo.html` - Comprehensive demo with all features
- `flatpickr-themes-demo.html` - Compare with other themes

## Browser Support

- Chrome/Edge 88+
- Firefox 78+
- Safari 14+

## License

MIT License - feel free to use in your projects!

## Contributing

Feel free to submit issues and pull requests to improve this theme.
