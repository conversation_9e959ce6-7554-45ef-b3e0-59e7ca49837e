query getProducts($first: Int, $variantsFirst: Int, $query:String) {
  products(first: $first, query: $query) {
    edges {
      cursor
      node {
        variants(first: $variantsFirst) {
          edges {
            node {
              id
            }
          }
        }
      }
    }
  }
}
  
{
  "first":250,
  "variantsFirst":10,
  "query":"9592846582040 OR 9592856150296"
}

=> 9592846582040 and 9592856150296 is product id


Variables