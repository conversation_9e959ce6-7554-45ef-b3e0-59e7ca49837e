/* Blue Theme Flatpickr Styles - Based on Image Design */

:root {
  /* Blue Theme Color Palette */
  --blue-primary: #007AFF;
  --blue-primary-light: #5AC8FA;
  --blue-background: #ffffff;
  --blue-foreground: #000000;
  --blue-text-light: #8E8E93;
  --blue-text-lighter: #C7C7CC;
  --blue-border: #E5E5EA;
  --blue-hover: #F2F2F7;
  --blue-selected-bg: #E3F2FD;
  
  --blue-radius: 20px;
  --blue-radius-sm: 12px;
  --blue-radius-button: 8px;
  
  --blue-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

/* Flatpickr Calendar Container */
.flatpickr-calendar.blue-theme {
  background: var(--blue-background) !important;
  border: none !important;
  border-radius: var(--blue-radius) !important;
  box-shadow: var(--blue-shadow) !important;
  padding: 24px !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif !important;
  font-size: 16px !important;
  line-height: 1.4 !important;
  color: var(--blue-foreground) !important;
  width: 320px !important;
  animation: blueSlideIn 0.3s ease-out !important;
}

/* Calendar Header */
.flatpickr-calendar.blue-theme .flatpickr-months {
  padding-bottom: 20px !important;
  margin-bottom: 16px !important;
  border-bottom: none !important;
}

.flatpickr-calendar.blue-theme .flatpickr-month {
  background: transparent !important;
  color: var(--blue-foreground) !important;
  height: auto !important;
}

.flatpickr-calendar.blue-theme .flatpickr-current-month {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: var(--blue-foreground) !important;
  padding: 0 !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flatpickr-calendar.blue-theme .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: transparent !important;
  border: none !important;
  color: var(--blue-foreground) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  padding: 0 !important;
  margin: 0 8px !important;
  cursor: pointer !important;
}

.flatpickr-calendar.blue-theme .numInputWrapper {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  width: auto !important;
}

.flatpickr-calendar.blue-theme .numInputWrapper input {
  color: var(--blue-foreground) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  background: transparent !important;
  border: none !important;
  text-align: center !important;
  width: 60px !important;
}

/* Navigation Arrows */
.flatpickr-calendar.blue-theme .flatpickr-prev-month,
.flatpickr-calendar.blue-theme .flatpickr-next-month {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  background: transparent !important;
  border: none !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--blue-text-light) !important;
  cursor: pointer !important;
  top: 20px !important;
}

.flatpickr-calendar.blue-theme .flatpickr-prev-month:hover,
.flatpickr-calendar.blue-theme .flatpickr-next-month:hover {
  background: var(--blue-hover) !important;
  color: var(--blue-foreground) !important;
}

.flatpickr-calendar.blue-theme .flatpickr-prev-month svg,
.flatpickr-calendar.blue-theme .flatpickr-next-month svg {
  width: 16px !important;
  height: 16px !important;
  fill: currentColor !important;
}

/* Weekdays */
.flatpickr-calendar.blue-theme .flatpickr-weekdays {
  background: transparent !important;
  margin: 12px 0 16px 0 !important;
  padding: 0 !important;
}

.flatpickr-calendar.blue-theme .flatpickr-weekday {
  background: transparent !important;
  color: var(--blue-text-light) !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: 8px 0 !important;
  text-align: center !important;
  border-radius: 0 !important;
  margin: 0 !important;
  width: 40px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Calendar Days */
.flatpickr-calendar.blue-theme .flatpickr-days {
  width: 100% !important;
  padding: 0 !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day {
  background: transparent !important;
  border: none !important;
  border-radius: 50% !important;
  color: var(--blue-foreground) !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  height: 40px !important;
  width: 40px !important;
  line-height: 40px !important;
  margin: 2px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day:hover {
  background: var(--blue-hover) !important;
  color: var(--blue-foreground) !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.selected {
  background: var(--blue-primary) !important;
  color: white !important;
  font-weight: 500 !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.today {
  background: var(--blue-selected-bg) !important;
  color: var(--blue-primary) !important;
  font-weight: 500 !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.today:hover {
  background: var(--blue-selected-bg) !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.today.selected {
  background: var(--blue-primary) !important;
  color: white !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.prevMonthDay,
.flatpickr-calendar.blue-theme .flatpickr-day.nextMonthDay {
  color: var(--blue-text-lighter) !important;
  opacity: 0.5 !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.disabled {
  color: var(--blue-text-lighter) !important;
  cursor: not-allowed !important;
  background: transparent !important;
  opacity: 0.3 !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.disabled:hover {
  background: transparent !important;
  color: var(--blue-text-lighter) !important;
  cursor: not-allowed !important;
}

/* Range Selection */
.flatpickr-calendar.blue-theme .flatpickr-day.inRange {
  background: var(--blue-selected-bg) !important;
  color: var(--blue-primary) !important;
  border-radius: 0 !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.startRange {
  background: var(--blue-primary) !important;
  color: white !important;
  border-radius: 50% 0 0 50% !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.endRange {
  background: var(--blue-primary) !important;
  color: white !important;
  border-radius: 0 50% 50% 0 !important;
}

.flatpickr-calendar.blue-theme .flatpickr-day.startRange.endRange {
  border-radius: 50% !important;
}

/* Action Buttons */
.flatpickr-calendar.blue-theme::after {
  content: '';
  display: block;
  width: 100%;
  height: 1px;
  background: var(--blue-border);
  margin: 20px 0 16px 0;
}

.blue-theme-actions {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-top: 20px !important;
  padding-top: 16px !important;
  border-top: 1px solid var(--blue-border) !important;
}

.blue-theme-btn {
  padding: 12px 24px !important;
  border-radius: var(--blue-radius-button) !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border: none !important;
  outline: none !important;
}

.blue-theme-btn.cancel {
  background: transparent !important;
  color: var(--blue-text-light) !important;
}

.blue-theme-btn.cancel:hover {
  background: var(--blue-hover) !important;
  color: var(--blue-foreground) !important;
}

.blue-theme-btn.apply {
  background: var(--blue-primary) !important;
  color: white !important;
}

.blue-theme-btn.apply:hover {
  background: var(--blue-primary-light) !important;
}

/* Input Field Styling */
.blue-datepicker-input {
  width: 100%;
  padding: 16px 20px;
  font-size: 16px;
  line-height: 1.4;
  color: var(--blue-foreground);
  background: var(--blue-background);
  border: 1px solid var(--blue-border);
  border-radius: var(--blue-radius-sm);
  transition: all 0.2s ease;
  box-sizing: border-box;
  font-weight: 400;
}

.blue-datepicker-input:hover {
  border-color: var(--blue-text-light);
}

.blue-datepicker-input:focus {
  outline: none;
  border-color: var(--blue-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.blue-datepicker-input::placeholder {
  color: var(--blue-text-lighter);
  font-weight: 400;
}

/* Animation */
.flatpickr-calendar.blue-theme.open {
  animation: blueSlideIn 0.3s ease-out;
}

@keyframes blueSlideIn {
  from {
    opacity: 0;
    transform: translateY(-12px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
