/* Basic Reset & Body Style */
body {
    margin: 0;
    font-family: 'Inter', sans-serif; /* Using Inter font, similar to many modern UIs */
    background-color: #1a1a2e; /* Dark background for context */
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    color: #e0e0e0; /* Light text color */
}

/* Main Card Styling */
.focus-bubble-card {
    background: linear-gradient(145deg, #3a2a4c, #2a1d3a); /* Dark purple gradient */
    border-radius: 18px;
    padding: 25px;
    width: 320px;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    gap: 15px; /* Spacing between main sections */
}

/* Header */
.card-header {
    text-align: center;
    font-weight: 500;
    font-size: 0.9em;
    color: #a0a0c0; /* Lighter purple/gray */
    letter-spacing: 1px;
    margin-bottom: 10px;
}

/* Timer Section */
.timer-section {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
}

.timer-circle {
    width: 160px;
    height: 160px;
    background-color: #7d212b; /* Dark red/maroon */
    /* Optional: Add a subtle texture or inner shadow if needed */
    /* background-image: url('path/to/texture.png'); */
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.2);
}

.time-display {
    font-size: 2.8em;
    font-weight: 700;
    color: #ffffff;
}

/* Motto */
.motto {
    text-align: center;
    font-size: 1.1em;
    color: #ffffff;
    margin: 10px 0;
}

/* Divider Lines */
.divider {
    border: none;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.15);
    margin: 10px 0; /* Adjusted margin */
}

/* Stats Section */
.stats-section {
    margin-top: 5px;
}

.stats-title {
    font-size: 0.95em;
    font-weight: 500;
    color: #b0b0d0; /* Slightly lighter gray/purple */
    margin: 10px 0 10px 0;
    text-align: left;
}

.website-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.website-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    font-size: 0.9em;
}

.website-name {
    color: #d0d0d0; /* Slightly dimmer white */
}

.time-spent {
    color: #b0b0d0; /* Match stats title color */
    font-weight: 500;
}

/* Footer */
.card-footer {
    margin-top: 5px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px; /* Space above footer items */
}

.lofi-section,
.focus-mode-section {
    display: flex;
    align-items: center;
    font-size: 0.9em;
    color: #c0c0e0;
}

.lofi-section .icon {
    margin-right: 8px;
    font-size: 1.1em; /* Make icon slightly larger */
}

/* Simple Toggle Switch Visualization */
.focus-mode-section span {
    margin-right: 12px;
}

.toggle-switch {
    width: 40px;
    height: 22px;
    background-color: #555; /* Default off state color */
    border-radius: 11px; /* Pill shape */
    position: relative;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.toggle-knob {
    width: 18px;
    height: 18px;
    background-color: white;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px; /* Off state position */
    transition: transform 0.3s ease;
}

/* Active state for toggle */
.toggle-switch.active {
    background-color: #3b82f6; /* Blue color when active */
}

.toggle-switch.active .toggle-knob {
    transform: translateX(18px); /* Move knob to the right */
}