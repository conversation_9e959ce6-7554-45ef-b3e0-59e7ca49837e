<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DatePicker Library - HeroUI Style</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="datepicker.css">
    <!-- Removed redundant Tailwind color configuration -->
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">DatePicker Library</h1>
        <p class="text-gray-600 mb-12">A comprehensive datepicker library with time slot support, built with HTML, Tailwind CSS, and JavaScript.</p>

        <!-- Demo Section -->
        <section class="mb-12">
            <h2 class="text-2xl font-semibold text-gray-800 mb-6">Interactive Demo</h2>
            <div class="bg-white p-8 rounded-lg shadow-sm border">
                <div class="max-w-md mx-auto">
                    <div class="datepicker" data-variant="bordered" data-size="lg" data-show-time="true" data-time-slots="true" data-disable-past-dates="true" data-time-slot-start="7" data-time-slot-end="13" data-time-slot-interval="30" id="demo-picker">
                        <label class="block text-lg font-medium text-gray-700 mb-3">Select Future Date and Time</label>
                        <div class="datepicker-input-wrapper">
                            <input type="text" class="datepicker-input" placeholder="Click to select future date and time" readonly>
                            <button class="datepicker-trigger" type="button">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">Morning slots: 7:00 AM - 1:00 PM (30-minute intervals)</p>
                    </div>

                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">Selected Value:</h4>
                        <p id="selected-value" class="text-gray-600">No date selected</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Calendar popups will be dynamically created by JavaScript for each datepicker instance -->

    <script src="datepicker.js"></script>
</body>
</html>