<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Collapsible Text</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }

    .text-container {
      position: relative;
      width: 100%;
      max-width: 600px;
      margin: 20px auto;
    }

    .text-content {
      max-height: 60px;
      overflow: hidden;
      transition: max-height 0.5s ease; /* Animation */
    }

    .text-content.expanded {
      max-height: 500px; /* Enough to display all content */
    }

    .toggle-btn {
      display: inline-block;
      margin-top: 10px;
      padding: 5px 10px;
      background-color: #007bff;
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .toggle-btn:hover {
      background-color: #0056b3;
    }

    .toggle-btn.hidden {
      display: none; /* Ẩn nút */
    }
  </style>
</head>
<body>
  <div class="text-container">
    <p class="text-content">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque nec velit venenatis, volutpat odio sed, 
      vehicula lectus. Nam viverra ligula ut tellus gravida, vel malesuada erat ultrices. Nulla facilisi. Sed 
      sit amet elit a lorem dictum tincidunt ut id odio.
    </p>
    <button class="toggle-btn">Show more</button>
  </div>

  <div class="text-container">
    <p class="text-content">
      Short text, no need to collapse.
    </p>
    <button class="toggle-btn">Show more</button>
  </div>

  <div class="text-container">
    <p class="text-content">
      Aenean interdum, purus sed aliquet dignissim, eros lectus facilisis tortor, vitae eleifend eros libero at 
      nisi. Fusce a libero at justo malesuada vehicula. Ut bibendum congue magna vel placerat.
    </p>
    <button class="toggle-btn">Show more</button>
  </div>

  <script>
    // Lấy tất cả các phần tử text-container
    const textContainers = document.querySelectorAll('.text-container');

    textContainers.forEach(container => {
      const textContent = container.querySelector('.text-content'); // Đoạn text
      const toggleBtn = container.querySelector('.toggle-btn'); // Nút bấm

      // Kiểm tra chiều cao thực tế của đoạn text
      if (textContent.scrollHeight <= 60) {
        toggleBtn.classList.add('hidden'); // Ẩn nút nếu nội dung không cần collapse
      }

      // Đo chiều cao đầy đủ của đoạn text
      const fullHeight = textContent.scrollHeight;

      // Gắn sự kiện click cho nút
      toggleBtn.addEventListener('click', () => {
        if (textContent.style.maxHeight === '60px' || !textContent.style.maxHeight) {
          textContent.style.maxHeight = `${fullHeight}px`; // Mở rộng
          toggleBtn.textContent = 'Show less';
        } else {
          textContent.style.maxHeight = '60px'; // Thu gọn
          toggleBtn.textContent = 'Show more';
        }
      });
    });
  </script>
</body>
</html>
