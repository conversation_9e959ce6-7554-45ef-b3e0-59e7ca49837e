<!DOCTYPE html>
<html lang="en">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Double Range Slider</title>
    <!--Google Fonts-->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@500&display=swap" rel="stylesheet">
    <!--Stylesheet-->
    <style>
        .wrapper {
            position: relative;
            width: 95vmin;
            background-color: #ffffff;
            padding: 50px 40px 20px 40px;
            border-radius: 10px;
        }

        .container {
            position: relative;
            width: 100%;
            height: 100px;
            margin-top: 30px;
        }

        input[type="range"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 100%;
            outline: none;
            position: absolute;
            margin: auto;
            top: 0;
            bottom: 0;
            background-color: transparent;
            pointer-events: none;
        }

        .slider-track {
            width: 100%;
            height: 5px;
            position: absolute;
            margin: auto;
            top: 0;
            bottom: 0;
            border-radius: 5px;
            background: #8080802b;
        }

        input[type="range"]::-webkit-slider-runnable-track {
            -webkit-appearance: none;
            height: 5px;
        }

        input[type="range"]::-moz-range-track {
            -moz-appearance: none;
            height: 5px;
        }

        input[type="range"]::-ms-track {
            appearance: none;
            height: 5px;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            height: 1.7em;
            width: 1.7em;
            background-color: #ffffff;
            border: 1px solid #ccc;
            cursor: pointer;
            margin-top: -9px;
            pointer-events: auto;
            border-radius: 50%;
        }

        input[type="range"]::-moz-range-thumb {
            -webkit-appearance: none;
            height: 1.7em;
            width: 1.7em;
            cursor: pointer;
            border-radius: 50%;
            background-color: #ffffff;
            border: 1px solid #ccc;
            pointer-events: auto;
        }

        input[type="range"]::-ms-thumb {
            appearance: none;
            height: 1.7em;
            width: 1.7em;
            cursor: pointer;
            border-radius: 50%;
            background-color: #ffffff;
            border: 1px solid #ccc;
            pointer-events: auto;
        }

        input[type="range"]:active::-webkit-slider-thumb {
            background-color: #ffffff;
            border: 1px solid #ccc;
        }

        .values {
            background-color: #fff;
            width: 32%;
            position: relative;
            margin: auto;
            padding: 10px 0;
            border-radius: 5px;
            text-align: center;
            font-weight: 500;
            font-size: 25px;
            color: #ffffff;
        }

        .values:before {
            content: "";
            position: absolute;
            height: 0;
            width: 0;
            border-top: 15px solid #ccc;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            margin: auto;
            bottom: -14px;
            left: 0;
            right: 0;
        }
    </style>
</head>

<body>
    <div class="wrapper">
        <div class="">
            <span id="range1">
            </span>
            <span> &dash; </span>
            <span id="range2">
            </span>
        </div>
        <div class="container">
            <div class="slider-track"></div>
            <input type="range" min="0" max="100" value="0" id="slider-1" oninput="slideOne()">
            <input type="range" min="0" max="100" value="100" id="slider-2" oninput="slideTwo()">
        </div>
    </div>

    <!--Script-->
    <script>
        window.onload = function () {
            slideOne();
            slideTwo();
        }

        let sliderOne = document.getElementById("slider-1");
        let sliderTwo = document.getElementById("slider-2");
        let displayValOne = document.getElementById("range1");
        let displayValTwo = document.getElementById("range2");
        let minGap = 0;
        let sliderTrack = document.querySelector(".slider-track");
        let sliderMaxValue = document.getElementById("slider-1").max;

        function slideOne() {
            if (parseInt(sliderTwo.value) - parseInt(sliderOne.value) <= minGap) {
                sliderOne.value = parseInt(sliderTwo.value) - minGap;
            }
            displayValOne.textContent = sliderOne.value;
            fillColor();
        }
        function slideTwo() {
            if (parseInt(sliderTwo.value) - parseInt(sliderOne.value) <= minGap) {
                sliderTwo.value = parseInt(sliderOne.value) + minGap;
            }
            displayValTwo.textContent = sliderTwo.value;
            fillColor();
        }
        function fillColor() {
            percent1 = (sliderOne.value / sliderMaxValue) * 100;
            percent2 = (sliderTwo.value / sliderMaxValue) * 100;
            sliderTrack.style.background = `linear-gradient(to right, #dadae5 ${percent1}% , #3264fe ${percent1}% , #3264fe ${percent2}%, #dadae5 ${percent2}%)`;
        }
    </script>
</body>

</html>