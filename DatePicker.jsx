import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, CalendarIcon, ClockIcon } from '@heroicons/react/24/outline';

const DatePicker = ({
  variant = 'flat',
  size = 'md',
  color = 'default',
  showTime = false,
  timeSlots = false,
  range = false,
  required = false,
  minDate = null,
  maxDate = null,
  disabledDates = null,
  disablePastDates = 'true',
  timeSlotStart = 7,
  timeSlotEnd = 13,
  timeSlotInterval = 30,
  label = '',
  placeholder = 'Select date',
  description = '',
  onChange = () => {},
  value = null,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(value);
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [timeSlotList, setTimeSlotList] = useState([]);
  
  const popupRef = useRef(null);
  const inputRef = useRef(null);

  // Generate time slots
  useEffect(() => {
    if (timeSlots) {
      const slots = [];
      for (let hour = timeSlotStart; hour <= timeSlotEnd; hour++) {
        for (let minute = 0; minute < 60; minute += timeSlotInterval) {
          if (hour === timeSlotEnd && minute > 0) break;
          
          const time12 = formatTime24To12(hour, minute);
          slots.push({
            time: time12,
            available: Math.random() > 0.3 // Randomly make some slots unavailable
          });
        }
      }
      setTimeSlotList(slots);
    }
  }, [timeSlots, timeSlotStart, timeSlotEnd, timeSlotInterval]);

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target) && 
          inputRef.current && !inputRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle ESC key
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, []);

  const formatTime24To12 = (hour, minute) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour > 12 ? hour - 12 : (hour === 0 ? 12 : hour);
    return `${hour12}:${minute.toString().padStart(2, '0')} ${period}`;
  };

  const formatDate = (date) => {
    if (!date) return '';
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  };

  const isDateDisabled = (date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);

    // Disable past dates
    if (disablePastDates && disablePastDates !== 'false') {
      if (disablePastDates === 'including-today') {
        if (compareDate <= today) return true;
      } else {
        if (compareDate < today) return true;
      }
    }

    // Check min/max dates
    if (minDate && date < minDate) return true;
    if (maxDate && date > maxDate) return true;

    // Check disabled dates patterns
    if (disabledDates === 'weekends') {
      const dayOfWeek = date.getDay();
      return dayOfWeek === 0 || dayOfWeek === 6;
    }

    return false;
  };

  const renderCalendar = () => {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(
        <div key={`empty-${i}`} className="calendar-day other-month"></div>
      );
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const currentDate = new Date(currentYear, currentMonth, day);
      const today = new Date();
      const isToday = currentDate.toDateString() === today.toDateString();
      const isSelected = selectedDate && currentDate.toDateString() === selectedDate.toDateString();
      const isDisabled = isDateDisabled(currentDate);

      days.push(
        <div
          key={day}
          className={`calendar-day ${isToday ? 'today' : ''} ${isSelected ? 'selected' : ''} ${isDisabled ? 'disabled' : ''}`}
          onClick={() => !isDisabled && handleDateSelect(currentDate)}
        >
          {day}
        </div>
      );
    }

    return (
      <div className="calendar-grid">
        <div className="calendar-weekdays">
          {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
            <div key={day} className="calendar-weekday">{day}</div>
          ))}
        </div>
        <div className="calendar-days">
          {days}
        </div>
      </div>
    );
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);
  };

  const handleTimeSlotSelect = (timeSlot) => {
    setSelectedTimeSlot(timeSlot);
  };

  const handleConfirm = () => {
    if (selectedDate) {
      let dateString = formatDate(selectedDate);
      
      if (showTime && selectedTime) {
        dateString += ' ' + selectedTime;
      } else if (timeSlots && selectedTimeSlot) {
        dateString += ' ' + selectedTimeSlot;
      }
      
      onChange({
        date: selectedDate,
        time: selectedTime,
        timeSlot: selectedTimeSlot,
        formatted: dateString
      });
    }
    setIsOpen(false);
  };

  const handleCancel = () => {
    setIsOpen(false);
  };

  const previousMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  const nextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  // Get variant classes
  const getVariantClasses = () => {
    const baseClasses = "w-full transition-all duration-200 cursor-pointer";
    
    switch (variant) {
      case 'flat':
        return `${baseClasses} bg-slate-50 border border-transparent focus:bg-white focus:border-blue-500 focus:ring-3 focus:ring-blue-100`;
      case 'bordered':
        return `${baseClasses} bg-white border-2 border-gray-200 focus:border-blue-500 focus:ring-3 focus:ring-blue-100`;
      case 'faded':
        return `${baseClasses} bg-slate-100 border border-slate-300 focus:bg-white focus:border-blue-500 focus:ring-3 focus:ring-blue-100`;
      case 'underlined':
        return `${baseClasses} bg-transparent border-0 border-b-2 border-gray-200 rounded-none px-0 focus:border-blue-500 focus:ring-0 focus:shadow-[0_2px_0_0_rgba(59,130,246,0.1)]`;
      default:
        return baseClasses;
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2 pr-10 text-xs';
      case 'lg':
        return 'px-5 py-4 pr-14 text-base';
      default:
        return 'px-4 py-3 pr-12 text-sm';
    }
  };

  const displayValue = () => {
    if (!selectedDate) return '';
    let dateString = formatDate(selectedDate);
    
    if (showTime && selectedTime) {
      dateString += ' ' + selectedTime;
    } else if (timeSlots && selectedTimeSlot) {
      dateString += ' ' + selectedTimeSlot;
    }
    
    return dateString;
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  return (
    <div className={`datepicker relative w-full ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      
      <div className="datepicker-input-wrapper relative" ref={inputRef}>
        <input
          type="text"
          className={`${getVariantClasses()} ${getSizeClasses()} rounded-lg outline-none`}
          placeholder={placeholder}
          value={displayValue()}
          onClick={() => setIsOpen(true)}
          readOnly
        />
        <button
          type="button"
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 p-1 rounded transition-colors ${
            size === 'sm' ? 'right-2' : size === 'lg' ? 'right-4' : 'right-3'
          }`}
          onClick={() => setIsOpen(true)}
        >
          {showTime || timeSlots ? (
            <ClockIcon className={size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'} />
          ) : (
            <CalendarIcon className={size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'} />
          )}
        </button>
      </div>

      {description && (
        <p className="text-sm text-gray-500 mt-2">{description}</p>
      )}

      {/* Calendar Popup */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div 
            ref={popupRef}
            className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-11/12 max-h-[90vh] overflow-y-auto transform transition-all duration-200 scale-100"
          >
            {/* Calendar Header */}
            <div className="flex items-center justify-between mb-4">
              <button
                type="button"
                onClick={previousMonth}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronLeftIcon className="w-5 h-5" />
              </button>
              <h3 className="text-lg font-semibold text-gray-900">
                {monthNames[currentMonth]} {currentYear}
              </h3>
              <button
                type="button"
                onClick={nextMonth}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronRightIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Calendar Grid */}
            {renderCalendar()}

            {/* Time Slots */}
            {timeSlots && (
              <div className="border-t border-gray-200 pt-4 mt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Available Time Slots</h4>
                <div className="grid grid-cols-3 gap-2 max-h-48 overflow-y-auto">
                  {timeSlotList.map((slot, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`p-2 text-sm border rounded-lg transition-all ${
                        !slot.available
                          ? 'bg-gray-50 text-gray-400 cursor-not-allowed border-gray-200'
                          : selectedTimeSlot === slot.time
                          ? 'bg-blue-500 text-white border-blue-500'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-blue-500 hover:bg-blue-50'
                      }`}
                      onClick={() => slot.available && handleTimeSlotSelect(slot.time)}
                      disabled={!slot.available}
                    >
                      {slot.time}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Footer Actions */}
            <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleConfirm}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600 transition-colors"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatePicker;
