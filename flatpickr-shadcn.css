/* shadcn/ui Inspired Flatpickr Styles */

:root {
  /* shadcn/ui Color Palette - Light Theme */
  --shadcn-background: 0 0% 100%;
  --shadcn-foreground: 222.2 84% 4.9%;
  --shadcn-card: 0 0% 100%;
  --shadcn-card-foreground: 222.2 84% 4.9%;
  --shadcn-popover: 0 0% 100%;
  --shadcn-popover-foreground: 222.2 84% 4.9%;
  --shadcn-primary: 221.2 83.2% 53.3%;
  --shadcn-primary-foreground: 210 40% 98%;
  --shadcn-secondary: 210 40% 96%;
  --shadcn-secondary-foreground: 222.2 84% 4.9%;
  --shadcn-muted: 210 40% 96%;
  --shadcn-muted-foreground: 215.4 16.3% 46.9%;
  --shadcn-accent: 210 40% 96%;
  --shadcn-accent-foreground: 222.2 84% 4.9%;
  --shadcn-destructive: 0 84.2% 60.2%;
  --shadcn-destructive-foreground: 210 40% 98%;
  --shadcn-border: 214.3 31.8% 91.4%;
  --shadcn-input: 214.3 31.8% 91.4%;
  --shadcn-ring: 221.2 83.2% 53.3%;
  --shadcn-radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* shadcn/ui Color Palette - Dark Theme */
    --shadcn-background: 222.2 84% 4.9%;
    --shadcn-foreground: 210 40% 98%;
    --shadcn-card: 222.2 84% 4.9%;
    --shadcn-card-foreground: 210 40% 98%;
    --shadcn-popover: 222.2 84% 4.9%;
    --shadcn-popover-foreground: 210 40% 98%;
    --shadcn-primary: 217.2 91.2% 59.8%;
    --shadcn-primary-foreground: 222.2 84% 4.9%;
    --shadcn-secondary: 217.2 32.6% 17.5%;
    --shadcn-secondary-foreground: 210 40% 98%;
    --shadcn-muted: 217.2 32.6% 17.5%;
    --shadcn-muted-foreground: 215 20.2% 65.1%;
    --shadcn-accent: 217.2 32.6% 17.5%;
    --shadcn-accent-foreground: 210 40% 98%;
    --shadcn-destructive: 0 62.8% 30.6%;
    --shadcn-destructive-foreground: 210 40% 98%;
    --shadcn-border: 217.2 32.6% 17.5%;
    --shadcn-input: 217.2 32.6% 17.5%;
    --shadcn-ring: 224.3 76.3% 94.1%;
  }
}

/* Flatpickr Calendar Container */
.flatpickr-calendar.shadcn-theme {
  background: hsl(var(--shadcn-popover)) !important;
  border: 1px solid hsl(var(--shadcn-border)) !important;
  border-radius: var(--shadcn-radius) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  padding: 1rem !important;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  color: hsl(var(--shadcn-popover-foreground)) !important;
}

/* Calendar Header */
.flatpickr-calendar.shadcn-theme .flatpickr-months {
  padding-bottom: 0.75rem !important;
  margin-bottom: 0.75rem !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-month {
  background: transparent !important;
  color: hsl(var(--shadcn-foreground)) !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-current-month {
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: hsl(var(--shadcn-foreground)) !important;
  padding: 0 !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: hsl(var(--shadcn-background)) !important;
  border: 1px solid hsl(var(--shadcn-border)) !important;
  border-radius: calc(var(--shadcn-radius) - 2px) !important;
  color: hsl(var(--shadcn-foreground)) !important;
  font-weight: 500 !important;
  padding: 0.25rem 0.5rem !important;
}

.flatpickr-calendar.shadcn-theme .numInputWrapper {
  background: hsl(var(--shadcn-background)) !important;
  border: 1px solid hsl(var(--shadcn-border)) !important;
  border-radius: calc(var(--shadcn-radius) - 2px) !important;
  padding: 0.25rem 0.5rem !important;
}

.flatpickr-calendar.shadcn-theme .numInputWrapper input {
  color: hsl(var(--shadcn-foreground)) !important;
  font-weight: 500 !important;
}

/* Navigation Arrows */
.flatpickr-calendar.shadcn-theme .flatpickr-prev-month,
.flatpickr-calendar.shadcn-theme .flatpickr-next-month {
  width: 2rem !important;
  height: 2rem !important;
  border-radius: calc(var(--shadcn-radius) - 2px) !important;
  background: transparent !important;
  border: 1px solid transparent !important;
  transition: all 0.15s ease-in-out !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: hsl(var(--shadcn-muted-foreground)) !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-prev-month:hover,
.flatpickr-calendar.shadcn-theme .flatpickr-next-month:hover {
  background: hsl(var(--shadcn-accent)) !important;
  color: hsl(var(--shadcn-accent-foreground)) !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-prev-month svg,
.flatpickr-calendar.shadcn-theme .flatpickr-next-month svg {
  width: 1rem !important;
  height: 1rem !important;
  fill: currentColor !important;
}

/* Weekdays */
.flatpickr-calendar.shadcn-theme .flatpickr-weekdays {
  background: transparent !important;
  margin: 0.5rem 0 !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-weekday {
  background: transparent !important;
  color: hsl(var(--shadcn-muted-foreground)) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.025em !important;
  padding: 0.5rem 0.25rem !important;
  text-align: center !important;
}

/* Calendar Days */
.flatpickr-calendar.shadcn-theme .flatpickr-days {
  width: 100% !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day {
  background: transparent !important;
  border: 1px solid transparent !important;
  border-radius: calc(var(--shadcn-radius) - 2px) !important;
  color: hsl(var(--shadcn-foreground)) !important;
  font-size: 0.875rem !important;
  font-weight: 400 !important;
  height: 2.25rem !important;
  width: 2.25rem !important;
  line-height: 2rem !important;
  margin: 0.125rem !important;
  transition: all 0.15s ease-in-out !important;
  cursor: pointer !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day:hover {
  background: hsl(var(--shadcn-accent)) !important;
  color: hsl(var(--shadcn-accent-foreground)) !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day.selected {
  background: hsl(var(--shadcn-primary)) !important;
  color: hsl(var(--shadcn-primary-foreground)) !important;
  font-weight: 500 !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day.selected:hover {
  background: hsl(var(--shadcn-primary)) !important;
  opacity: 0.9 !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day.today {
  background: hsl(var(--shadcn-accent)) !important;
  color: hsl(var(--shadcn-accent-foreground)) !important;
  font-weight: 500 !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day.today:hover {
  background: hsl(var(--shadcn-accent)) !important;
  opacity: 0.8 !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day.prevMonthDay,
.flatpickr-calendar.shadcn-theme .flatpickr-day.nextMonthDay {
  color: hsl(var(--shadcn-muted-foreground)) !important;
  opacity: 0.5 !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day.disabled {
  color: hsl(var(--shadcn-muted-foreground)) !important;
  cursor: not-allowed !important;
  background: transparent !important;
  opacity: 0.3 !important;
}

.flatpickr-calendar.shadcn-theme .flatpickr-day.disabled:hover {
  background: transparent !important;
  color: hsl(var(--shadcn-muted-foreground)) !important;
  cursor: not-allowed !important;
}

/* Input Field Styling */
.shadcn-datepicker-input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: var(--shadcn-radius);
  border: 1px solid hsl(var(--shadcn-input));
  background-color: hsl(var(--shadcn-background));
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: hsl(var(--shadcn-foreground));
  transition: all 0.15s ease-in-out;
  box-sizing: border-box;
}

.shadcn-datepicker-input:hover {
  border-color: hsl(var(--shadcn-ring));
}

.shadcn-datepicker-input:focus {
  outline: none;
  border-color: hsl(var(--shadcn-ring));
  box-shadow: 0 0 0 2px hsl(var(--shadcn-ring) / 0.2);
}

.shadcn-datepicker-input::placeholder {
  color: hsl(var(--shadcn-muted-foreground));
}

/* Animation */
.flatpickr-calendar.shadcn-theme.open {
  animation: shadcnSlideIn 0.15s ease-out;
}

@keyframes shadcnSlideIn {
  from {
    opacity: 0;
    transform: translateY(-0.5rem) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Focus Ring */
.flatpickr-calendar.shadcn-theme .flatpickr-day:focus-visible {
  outline: 2px solid hsl(var(--shadcn-ring));
  outline-offset: 2px;
}
