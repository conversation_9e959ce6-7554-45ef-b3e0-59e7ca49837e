/* CSS Custom Properties (Variables) */
:root {
  /* HeroUI-inspired color palette */
  --primary: #006FEE;
  --primary-50: #e6f1fe;
  --primary-100: #cce3fd;
  --primary-200: #99c7fb;
  --primary-500: #006FEE;
  --primary-600: #005bc4;
  --primary-foreground: #ffffff;
  
  --default-50: #fafafa;
  --default-100: #f4f4f5;
  --default-200: #e4e4e7;
  --default-300: #d4d4d8;
  --default-400: #a1a1aa;
  --default-500: #71717a;
  --default-600: #52525b;
  --default-700: #3f3f46;
  --default-800: #27272a;
  --default-900: #18181b;
  
  --success: #17c964;
  --warning: #f5a524;
  --danger: #f31260;
  
  --background: #ffffff;
  --foreground: #11181c;
  --content1: #ffffff;
  --content2: #f4f4f5;
  --content3: #e4e4e7;
  --content4: #d4d4d8;
  
  --divider: #e4e4e7;
  --overlay: rgba(0, 0, 0, 0.5);
  
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 14px;
  
  --shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.02), 0px 2px 10px 0px rgb(0 0 0 / 0.06), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
  --shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.03), 0px 2px 30px 0px rgb(0 0 0 / 0.08), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
  --shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.04), 0px 30px 60px 0px rgb(0 0 0 / 0.12), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
}

/* Base Styles */
* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  min-height: 100vh;
  margin: 0;
  padding: 32px;
}

/* Layout Components */
.container {
  max-width: 768px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 48px;
}

.main-title {
  font-size: 30px;
  font-weight: 700;
  color: var(--default-900);
  margin: 0 0 8px 0;
}

.subtitle {
  color: var(--default-600);
  margin: 0;
  font-size: 16px;
}

.main-card {
  background: var(--content1);
  border-radius: 16px;
  box-shadow: var(--shadow-large);
  padding: 32px;
}

.datepicker-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--default-900);
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 14px;
  color: var(--default-600);
  margin: 0;
}

/* Month/Year Header */
.month-year-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--default-200);
  margin-bottom: 16px;
}

.month-year-display {
  font-size: 18px;
  font-weight: 600;
  color: var(--default-900);
}

.nav-buttons {
  display: flex;
  gap: 8px;
}

.nav-button {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
  background: var(--content2);
  border: 1px solid var(--default-200);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-button:hover:not(.disabled) {
  background: var(--primary-50);
  border-color: var(--primary-200);
}

.nav-button.disabled {
  opacity: 0.3;
  cursor: not-allowed;
  display: none;
}

.nav-button svg {
  width: 14px;
  height: 14px;
  fill: var(--default-600);
}

/* Input Wrapper Styles */
.input-wrapper {
  position: relative;
  background: var(--content1);
  border: 2px solid var(--default-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-small);
}

.input-wrapper:hover {
  border-color: var(--default-300);
  box-shadow: var(--shadow-medium);
}

.input-wrapper.focused {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 4px var(--primary-50);
}

.input-wrapper.error {
  border-color: var(--danger);
  box-shadow: 0 0 0 4px rgba(243, 18, 96, 0.1);
}

/* Floating Label Styles */
.floating-label {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: var(--content1);
  padding: 0 4px;
  color: var(--default-500);
  font-size: 14px;
  font-weight: 400;
  transition: all 0.2s ease;
  pointer-events: none;
  z-index: 1;
}

.floating-label.active {
  top: 0;
  font-size: 12px;
  font-weight: 500;
  color: var(--primary-500);
}

/* Input Styles */
.modern-input {
  width: 100%;
  padding: 16px 48px 16px 16px;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  font-weight: 400;
  color: var(--foreground);
  line-height: 1.5;
}

.modern-input::placeholder {
  color: var(--default-400);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.modern-input:focus::placeholder {
  opacity: 1;
}

/* Calendar Icon Styles */
.calendar-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--default-400);
  transition: color 0.2s ease;
  pointer-events: none;
}

.input-wrapper:hover .calendar-icon,
.input-wrapper.focused .calendar-icon {
  color: var(--primary-500);
}

/* Enhanced Flatpickr Calendar Styling */
.flatpickr-calendar {
  background: var(--content1) !important;
  border: none !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-large) !important;
  padding: 16px !important;
  font-family: 'Inter', sans-serif !important;
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.flatpickr-calendar.open {
  z-index: 1000 !important;
}

/* Calendar header */
.flatpickr-months {
  /* padding: 0 0 25px 0 !important; */
}

.flatpickr-month {
  background: transparent !important;
  color: var(--foreground) !important;
}

.flatpickr-current-month {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: var(--foreground) !important;
  padding: 0 !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
  background: var(--content1) !important;
  border: 1px solid var(--default-200) !important;
  border-radius: var(--radius-sm) !important;
  color: var(--foreground) !important;
  font-weight: 500 !important;
}

.numInputWrapper {
  background: var(--content1) !important;
  border: 1px solid var(--default-200) !important;
  border-radius: var(--radius-sm) !important;
}

.numInputWrapper input {
  color: var(--foreground) !important;
  font-weight: 500 !important;
}

/* Navigation arrows */
.flatpickr-prev-month {
  width: 32px !important;
  height: 32px !important;
  border-radius: var(--radius-sm) !important;
  background: var(--content2) !important;
  border: 1px solid var(--default-200) !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flatpickr-prev-month.hidden {
  display: none !important;
}

.flatpickr-next-month {
  width: 32px !important;
  height: 32px !important;
  border-radius: var(--radius-sm) !important;
  background: var(--content2) !important;
  border: 1px solid var(--default-200) !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  top: 8px;
  margin-right: 20px;
}

.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
  background: var(--primary-50) !important;
  border-color: var(--primary-200) !important;
}

.flatpickr-prev-month svg,
.flatpickr-next-month svg {
  width: 14px !important;
  height: 14px !important;
  fill: var(--default-600) !important;
}

/* Weekdays */
.flatpickr-weekdays {
  background: transparent !important;
  margin: 8px 0 !important;
  width: 100% !important;
}

.flatpickr-weekday {
  background: var(--content2) !important;
  color: var(--default-600) !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  border-radius: var(--radius-sm) !important;
  margin: 1px !important;
  padding: 8px 4px !important;
  width: 36px !important;
  text-align: center !important;
  flex: 1 !important;
  min-width: 36px !important;
}

/* Calendar days */
.flatpickr-days {
  width: 100% !important;
  display: flex !important;
  flex-wrap: wrap !important;
}

.flatpickr-day {
  background: transparent !important;
  border: 1px solid transparent !important;
  border-radius: var(--radius-sm) !important;
  color: var(--foreground) !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  height: 36px !important;
  width: 36px !important;
  line-height: 34px !important;
  margin: 1px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  flex: 0 0 calc(14.28% - 2px) !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flatpickr-day:hover {
  background: var(--primary-50) !important;
  border-color: var(--primary-200) !important;
  color: var(--primary-600) !important;
}

.flatpickr-day.selected {
  background: var(--primary-500) !important;
  border-color: var(--primary-500) !important;
  color: var(--primary-foreground) !important;
  font-weight: 600 !important;
}

.flatpickr-day.today {
  background: var(--content3) !important;
  border-color: var(--default-300) !important;
  color: var(--foreground) !important;
  font-weight: 500 !important;
}

.flatpickr-day.today:hover {
  background: var(--primary-50) !important;
  border-color: var(--primary-200) !important;
}

.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay {
  color: var(--default-400) !important;
}

.flatpickr-day.disabled {
  color: var(--default-300) !important;
  cursor: not-allowed !important;
  background: var(--default-50) !important;
  opacity: 0.5 !important;
  text-decoration: line-through !important;
}

.flatpickr-day.disabled:hover {
  background: var(--default-50) !important;
  border-color: transparent !important;
  color: var(--default-300) !important;
  cursor: not-allowed !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 16px;
  }

  .main-card {
    padding: 24px;
  }

  .main-title {
    font-size: 24px;
  }

  .section-title {
    font-size: 18px;
  }

  .flatpickr-calendar {
    width: 90vw !important;
  }

  .flatpickr-day {
    height: 32px !important;
    width: 32px !important;
    line-height: 30px !important;
    font-size: 13px !important;
  }
}
