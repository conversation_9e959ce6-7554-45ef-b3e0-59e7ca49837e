// style option
class StyleOption extends HTMLElement {
    connectedCallback() {
        this.innerHTML = `
            <style>
                radio-group{
                    display: flex;
                    column-gap: 10px;
                }
                radio-group .radio-option {
                    display: inline-block;
                    margin-top: 3px;
                }
                radio-group radio-option {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    column-gap: 5px;
                }
                radio-group label {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                }
                radio-group input[type="radio"] {
                    margin: 0;
                    padding: 0;
                }
                radio-group.column {
                    flex-direction: column;
                }
            </style>
        `;
    }
}


// Radio option normal
class RadioOption extends HTMLElement {
    constructor() {
        super();
        this.addEventListener("change", () => {

        });
    }
    connectedCallback() {
        const value = this.getAttribute("value");
        const label = this.textContent.trim();
        const groupName = this.closest("radio-group")?.getAttribute("name") || "default";
        const isChecked = this.hasAttribute("checked") ? "checked" : "";

        this.innerHTML = `
            <div class="radio-option">
                <input type="radio" name="${groupName}" id="${groupName}-${value}" value="${value}" ${isChecked}>
                <label for="${groupName}-${value}">${label}</label>
            </div>
        `;
    }
}

class RadioGroup extends HTMLElement {
    connectedCallback() {
        this.classList.add("wrap_radio");
        const firstOption = this.querySelector("radio-option");
        if (firstOption) {
            firstOption.setAttribute("checked", "true");
        }
    }
}

// Define the custom elements
customElements.define("radio-group", RadioGroup);
customElements.define("radio-option", RadioOption);
customElements.define("style-option", StyleOption);
