<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>
        const data = [
    {
      "id": 84,
      "createdAt": "2025-05-05T09:06:24.000Z",
      "updatedAt": "2025-05-06T08:25:51.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 1,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 83,
      "createdAt": "2025-05-05T09:06:12.000Z",
      "updatedAt": "2025-05-05T09:06:12.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 5,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 82,
      "createdAt": "2025-05-05T09:06:02.000Z",
      "updatedAt": "2025-05-05T09:06:02.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 2,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 81,
      "createdAt": "2025-05-05T09:05:29.000Z",
      "updatedAt": "2025-05-05T09:05:29.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 1,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 80,
      "createdAt": "2025-05-05T09:05:14.000Z",
      "updatedAt": "2025-05-05T09:05:14.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 1,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 79,
      "createdAt": "2025-05-05T09:01:44.000Z",
      "updatedAt": "2025-05-05T09:01:44.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 1,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 78,
      "createdAt": "2025-05-05T08:58:36.000Z",
      "updatedAt": "2025-05-05T08:58:36.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 1,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 77,
      "createdAt": "2025-05-05T08:57:06.000Z",
      "updatedAt": "2025-05-05T08:57:06.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 1,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 76,
      "createdAt": "2025-05-05T08:56:57.000Z",
      "updatedAt": "2025-05-05T08:56:57.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 1,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    },
    {
      "id": 75,
      "createdAt": "2025-05-05T08:56:34.000Z",
      "updatedAt": "2025-05-05T08:56:34.000Z",
      "deletedAt": null,
      "cartId": 6,
      "serviceId": 39,
      "serviceType": "GP",
      "quantity": 1,
      "service": {
        "id": 39,
        "createdAt": "2025-05-03T13:35:47.000Z",
        "updatedAt": "2025-05-08T18:13:30.000Z",
        "deletedAt": null,
        "name": "t1.com",
        "type": null,
        "typePack": "DOMAIN",
        "price": 0,
        "description": null,
        "status": "APPROVED",
        "fieldType": "SPORT",
        "urlDemo": null,
        "isIndex": false,
        "isShow": true,
        "isSaleTextLink": true,
        "textLinkPrice": 200000,
        "textLinkDuration": 1,
        "textLinkNote": "note text link",
        "isFollowTextLink": true,
        "isHomeTextLink": true,
        "isFooterTextLink": false,
        "isSaleGuestPost": true,
        "guestPostPrice": "100000.00",
        "guestPostNote": "note gp",
        "note": null,
        "isFollowGuestPost": true,
        "isIndexGuestPost": true,
        "isSaleBanner": true,
        "bannerPrice": "400000.00",
        "bannerDuration": 2,
        "userId": 10,
        "user": {
          "id": 10,
          "createdAt": "2025-04-17T10:14:01.000Z",
          "updatedAt": "2025-04-17T10:14:01.000Z",
          "deletedAt": null,
          "username": "partner1",
          "email": null,
          "password": "$2b$10$QP.HDdt1u3lncxOq8RA5yeEQ4/xL5oJOHxD8ylgytYbjGIbXPeVOO",
          "roleId": 5,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        },
        "complimentaries": [
          {
            "id": 34,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          },
          {
            "id": 33,
            "createdAt": "2025-05-08T18:13:30.000Z",
            "updatedAt": "2025-05-08T18:13:30.000Z",
            "deletedAt": null,
            "name": "12323",
            "serviceId": 39
          }
        ],
        "refDomain": 0,
        "dr": 0,
        "organicTraffic": 0
      },
      "cart": {
        "id": 6,
        "createdAt": "2025-05-05T07:15:17.000Z",
        "updatedAt": "2025-05-05T07:15:17.000Z",
        "deletedAt": null,
        "userId": 46,
        "user": {
          "id": 46,
          "createdAt": "2025-05-05T07:02:54.000Z",
          "updatedAt": "2025-05-05T07:02:54.000Z",
          "deletedAt": null,
          "username": "test1",
          "email": "<EMAIL>",
          "password": "$2b$10$os/DWiHpaMlbrwf36SWa3u6de6kAK0Xq2aY3UibsNyeKqY5StM0Uu",
          "roleId": 2,
          "telegramUsername": null,
          "phone": null,
          "bankNameInCard": null,
          "bankNumber": null,
          "bankName": null,
          "usdt": null
        }
      }
    }
  ];

const grouped = Object.values(
    data.reduce((acc, item) => {
        const key = item.service.userId;
        if (!acc[key]) acc[key] = { ids: [], items: [], user: item.service.user };
        acc[key].ids.push(item.id);
        acc[key].items.push(item);
        return acc;
    }, {})
);

const result = grouped.flatMap((group, idx) => {
    const serviceName = group.user.username;
    const summary = {
        id: group.ids.join('-'),
        service: {
            name: serviceName,
            userId: group.user.id
        }
    };
    const items = group.items.map(item => ({
        ...item,
        service: {
            ...item.service,
            name: serviceName
        }
    }));
    return [summary, ...items];
});

console.log(result);

    </script>
</body>
</html>