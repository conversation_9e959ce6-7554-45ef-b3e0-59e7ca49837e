<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <style>
    .user {
  flex: 0 0 var(--size);
  width: var(--size);
  height: var(--size);
  background-color: lightgrey;
  border-radius: 50%;
}

.comment {
  --size: 2rem;
  position: relative;
  display: flex;
  gap: 0.5rem;
}

.comment p {
  font-family: "Flow Circular", sans-serif;
  color: #9a9a9a;
}



.comment__actions {
  display: flex;
  gap: 1rem;
  padding-block: 4px;
  padding-inline-start: 1rem;
}

.comment__actions a {
  text-decoration: none;
  color: #222;
  font-size: 14px;
  font-weight: bold;
}

.comment__body {
  background-color: #f0f2f5;
  padding: 1rem;
  border-radius: 15px;
}

.comment__body > * + * {
  margin-top: 0.5rem;
}

@container style(--depth: 0) {
  li:has(ul) > .comment {
    position: relative;
  }

  li:has(ul) > .comment::before {
    content: "";
    position: absolute;
    inset-inline-start: 15px;
    top: calc(var(--size) + 8px);
    bottom: 0;
    width: 2px;
    background: #000;
  }
}

@container style(--depth: 1) {
  li:has(ul) > .comment {
    position: relative;
  }

  li:has(ul) > .comment::before {
    content: "";
    position: absolute;
    inset-inline-start: 12px;
    top: calc(var(--size) + 8px);
    bottom: 0;
    width: 2px;
    background: #000;
  }

  li:not(:last-child) {
    position: relative;
  }

  li:not(:last-child)::before {
    content: "";
    position: absolute;
    inset-inline-start: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #000;
  }

  li::after {
    content: "";
    position: absolute;
    inset-inline-start: 15px;
    top: -2px;
    height: 20px;
    width: 28px;
    border-inline-start: 2px solid #000;
    border-bottom: 2px solid #000;
    background: transparent;
    border-end-start-radius: 10px;
  }
}

@container style(--depth: 2) {
  li {
    position: relative;
  }

  li::after {
    content: "";
    position: absolute;
    inset-inline-start: 12px;
    top: -2px;
    height: 20px;
    width: 28px;
    border-inline-start: 2px solid #000;
    border-bottom: 2px solid #000;
    background: transparent;
    border-end-start-radius: 10px;
  }

  li:not(:last-child)::before {
    content: "";
    position: absolute;
    inset-inline-start: 12px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #000;
  }
}

@container style(--nested: true) {
  .comment {
    position: relative;
    padding-top: 4px;
  }

  li {
    padding-inline-start: 3rem;
  }
}

.wrapper {
  max-width: 700px;
  margin: 1rem auto;
  padding: 1rem;
}

strong {
  font-weight: bold;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  font-family: "system-ui";
  line-height: 1.3;
}

.alert {
  background-color: color-mix(in hsl, yellow 20%, white);
  text-align: center;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

  </style>
</head>
<body>
<div class="wrapper">
  <ul class="list" depth="0" style="--depth: 0;">
    <li class="" style="--nested: true;">
      <div class="comment">
        <div class="user"></div>
        <div class="">
          <!-- body -->
          <div class="comment__body">
            <p><strong>Ahmad Shadeed</strong></p>
            <p>I like that, looks so cool and steady. This is the way to build such a high performant element in Javascript. I will keep doing it.</p>
          </div>
          <!-- actions -->
          <div class="comment__actions">
            <a href="#">Like</a>
            <a href="#">Reply</a>
          </div>
        </div>
      </div>
      <ul class="list" depth="1" style="--depth: 1">
        <li style="">
          <div class="comment">
            <div class="user"></div>
            <div class="">
              <!-- body -->
              <div class="comment__body">
                <p><strong>Ahmad Shadeed</strong></p>
                <p>I like that, looks so cool and steady. This is the way to build such a high performant element in Javascript. I will keep doing it.</p>
              </div>
              <!-- actions -->
              <div class="comment__actions">
                <a href="#">Like</a>
                <a href="#">Reply</a>
              </div>
            </div>
          </div>
        </li>
        <li style="--nested: true;">
          <div class="comment">
            <div class="user"></div>
            <div class="">
              <!-- body -->
              <div class="comment__body">
                <p><strong>Ahmad Shadeed</strong></p>
                <p>I like that, loo I will keep doing it.</p>
              </div>
              <!-- actions -->
              <div class="comment__actions">
                <a href="#">Like</a>
                <a href="#">Reply</a>
              </div>
            </div>
          </div>
          <ul class="list" depth="2" style="--depth: 2">
            <li style="">
              <div class="comment">
                <div class="user"></div>
                <div class="">
                  <!-- body -->
                  <div class="comment__body">
                    <p><strong>Ahmad Shadeed</strong></p>
                    <p>I like that, looks so cool and steady. This is the way to build such a high performant element in Javascript. I will keep doing it.</p>
                  </div>
                  <!-- actions -->
                  <div class="comment__actions">
                    <a href="#">Like</a>
                    <a href="#">Reply</a>
                  </div>
                </div>
              </div>
            </li>
            <li style="">
              <div class="comment">
                <div class="user"></div>
                <div class="">
                  <!-- body -->
                  <div class="comment__body">
                    <p><strong>Ahmad Shadeed</strong></p>
                    <p>I like that, looks so cool and steady. This is the way to build such a high performant element in Javascript. I will keep doing it.</p>
                  </div>
                  <!-- actions -->
                  <div class="comment__actions">
                    <a href="#">Like</a>
                    <a href="#">Reply</a>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </li>
        <li style="--nested: true;">
          <div class="comment">
            <div class="user"></div>
            <div class="">
              <!-- body -->
              <div class="comment__body">
                <p><strong>Ahmad Shadeed</strong></p>
                <p>I like that, looks so cool and steady. This is the way to build such a high performant element in Javascript. I will keep doing it.</p>
              </div>
              <!-- actions -->
              <div class="comment__actions">
                <a href="#">Like</a>
                <a href="#">Reply</a>
              </div>
            </div>
          </div>
        </li>
      </ul>

    </li>
    <li>
      <div class="comment">
        <div class="user"></div>
        <div class="">
          <!-- body -->
          <div class="comment__body">
            <p><strong>Ahmad Shadeed</strong></p>
            <p>I like that, looks so cool and steady. This is the way to build such a high performant element in Javascript. I will keep doing it.</p>
          </div>
          <!-- actions -->
          <div class="comment__actions">
            <a href="#">Like</a>
            <a href="#">Reply</a>
          </div>
        </div>
      </div>
    </li>
    <li>
      <div class="comment">
        <div class="user"></div>
        <div class="">
          <!-- body -->
          <div class="comment__body">
            <p><strong>Ahmad Shadeed</strong></p>
            <p>I like that, looks so cool and steady. This is the way to build such a high performant element in Javascript. I will keep doing it.</p>
          </div>
          <!-- actions -->
          <div class="comment__actions">
            <a href="#">Like</a>
            <a href="#">Reply</a>
          </div>
        </div>
      </div>
    </li>
  </ul>
</div>
</body>
</html>