<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flatpickr Custom Themes Demo</title>
    
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <!-- Custom Theme CSS Files -->
    <link rel="stylesheet" href="./flatpickr-antd.css">
    <link rel="stylesheet" href="./flatpickr-shadcn.css">
    <link rel="stylesheet" href="./flatpickr-heroui.css">
    <link rel="stylesheet" href="./flatpickr-flowbite.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.125rem;
            color: #4a5568;
        }
        
        .themes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .theme-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .theme-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .theme-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #1a202c;
        }
        
        .theme-description {
            color: #4a5568;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .input-group {
            margin-bottom: 1rem;
        }
        
        .input-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .theme-toggle {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .toggle-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }
        
        .toggle-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .code-section {
            background: #f8fafc;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .code-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 1rem;
        }
        
        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            overflow-x: auto;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .themes-grid {
                grid-template-columns: 1fr;
            }
            
            .theme-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Flatpickr Custom Themes</h1>
            <p>Beautiful datepicker styles inspired by popular UI libraries</p>
        </div>
        
        <div class="themes-grid">
            <!-- Ant Design Theme -->
            <div class="theme-card">
                <h2 class="theme-title">Ant Design Theme</h2>
                <p class="theme-description">
                    Clean and professional styling inspired by Ant Design's design system. 
                    Features subtle borders, blue accent colors, and smooth transitions.
                </p>
                
                <div class="theme-toggle">
                    <button class="toggle-btn active" onclick="toggleTheme('antd', 'light')">Light</button>
                    <button class="toggle-btn" onclick="toggleTheme('antd', 'dark')">Dark</button>
                </div>
                
                <div class="input-group">
                    <label class="input-label">Select Date</label>
                    <input type="text" id="antd-datepicker" class="antd-datepicker-input" placeholder="Choose a date...">
                </div>
            </div>
            
            <!-- shadcn Theme -->
            <div class="theme-card">
                <h2 class="theme-title">shadcn/ui Theme</h2>
                <p class="theme-description">
                    Modern and elegant styling based on shadcn/ui components. 
                    Supports both light and dark themes with subtle shadows and clean typography.
                </p>
                
                <div class="theme-toggle">
                    <button class="toggle-btn active" onclick="toggleTheme('shadcn', 'light')">Light</button>
                    <button class="toggle-btn" onclick="toggleTheme('shadcn', 'dark')">Dark</button>
                </div>
                
                <div class="input-group">
                    <label class="input-label">Select Date</label>
                    <input type="text" id="shadcn-datepicker" class="shadcn-datepicker-input" placeholder="Choose a date...">
                </div>
            </div>
            
            <!-- HeroUI Theme -->
            <div class="theme-card">
                <h2 class="theme-title">HeroUI Theme</h2>
                <p class="theme-description">
                    Vibrant and modern styling inspired by HeroUI.
                    Features gradient accents, smooth animations, and a contemporary design.
                </p>

                <div class="theme-toggle">
                    <button class="toggle-btn active" onclick="toggleTheme('heroui', 'light')">Light</button>
                    <button class="toggle-btn" onclick="toggleTheme('heroui', 'dark')">Dark</button>
                </div>

                <div class="input-group">
                    <label class="input-label">Select Date</label>
                    <input type="text" id="heroui-datepicker" class="heroui-datepicker-input" placeholder="Choose a date...">
                </div>
            </div>

            <!-- Flowbite Theme -->
            <div class="theme-card">
                <h2 class="theme-title">Flowbite Theme</h2>
                <p class="theme-description">
                    Professional styling using Flowbite design system with Tailwind CSS.
                    Features clean design, proper spacing, and excellent dark mode support.
                </p>

                <div class="theme-toggle">
                    <button class="toggle-btn active" onclick="toggleTheme('flowbite', 'light')">Light</button>
                    <button class="toggle-btn" onclick="toggleTheme('flowbite', 'dark')">Dark</button>
                </div>

                <div class="input-group">
                    <label class="input-label">Select Date</label>
                    <div style="position: relative; max-width: 20rem;">
                        <div style="position: absolute; top: 0; bottom: 0; left: 0; display: flex; align-items: center; padding-left: 0.75rem; pointer-events: none;">
                            <svg style="width: 1rem; height: 1rem; color: #6b7280;" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input type="text" id="flowbite-datepicker" class="flowbite-datepicker-input" placeholder="Choose a date..." style="width: 100%; padding: 0.625rem 0.75rem; padding-left: 2.5rem; font-size: 0.875rem; color: #111827; background-color: #f9fafb; border: 1px solid #d1d5db; border-radius: 0.75rem;">
                    </div>
                </div>
            </div>
        </div>
        
        
    </div>
    
    <!-- Flatpickr JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <script>
        // Initialize datepickers
        const antdPicker = flatpickr("#antd-datepicker", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('antd-theme');
            }
        });
        
        const shadcnPicker = flatpickr("#shadcn-datepicker", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('shadcn-theme');
            }
        });
        
        const herouiPicker = flatpickr("#heroui-datepicker", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('heroui-theme');
            }
        });

        const flowbitePicker = flatpickr("#flowbite-datepicker", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
            }
        });
        
        // Theme toggle functionality
        function toggleTheme(theme, mode) {
            // Update button states
            const card = event.target.closest('.theme-card');
            const buttons = card.querySelectorAll('.toggle-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Apply theme mode
            if (mode === 'dark') {
                document.documentElement.style.setProperty('color-scheme', 'dark');
                document.body.classList.add('dark-theme');
            } else {
                document.documentElement.style.setProperty('color-scheme', 'light');
                document.body.classList.remove('dark-theme');
            }
        }
    </script>
</body>
</html>
