/* DatePicker Library Styles with Flatpickr Integration */

/* Flatpickr Custom Styles - Override default styles */
.flatpickr-calendar {
    background-color: white !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    border: none !important;
    padding: 1.5rem !important;
    font-family: inherit !important;
}

.flatpickr-calendar.open {
    z-index: 1000 !important;
}

/* Flatpickr Header */
.flatpickr-months {
    padding-bottom: 1rem !important;
}

.flatpickr-month {
    background: transparent !important;
    color: #1f2937 !important;
    fill: #1f2937 !important;
}

.flatpickr-current-month {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
}

.flatpickr-prev-month,
.flatpickr-next-month {
    background: none !important;
    border: none !important;
    padding: 0.5rem !important;
    border-radius: 0.375rem !important;
    color: #6b7280 !important;
    transition: all 0.2s ease-in-out !important;
}

.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
    background-color: #f3f4f6 !important;
    color: #374151 !important;
}

/* Flatpickr Weekdays */
.flatpickr-weekdays {
    background: transparent !important;
    margin-bottom: 0.5rem !important;
}

.flatpickr-weekday {
    background: transparent !important;
    color: #6b7280 !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    padding: 0.5rem !important;
}

/* Flatpickr Days */
.flatpickr-days {
    padding: 0 !important;
}

.flatpickr-day {
    border-radius: 0.375rem !important;
    font-size: 0.875rem !important;
    transition: all 0.2s ease-in-out !important;
    border: 1px solid transparent !important;
    margin: 0.125rem !important;
    width: 2.5rem !important;
    height: 2.5rem !important;
    line-height: 2.25rem !important;
}

.flatpickr-day:hover {
    background-color: #f3f4f6 !important;
    border-color: transparent !important;
}

.flatpickr-day.selected {
    background-color: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

.flatpickr-day.today {
    border-color: #3b82f6 !important;
    color: #3b82f6 !important;
    font-weight: 600 !important;
    background: transparent !important;
}

.flatpickr-day.today.selected {
    background-color: #3b82f6 !important;
    color: white !important;
}

.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay {
    color: #d1d5db !important;
}

.flatpickr-day.disabled {
    color: #d1d5db !important;
    background-color: #f9fafb !important;
}

.flatpickr-day.disabled:hover {
    background-color: #f9fafb !important;
    cursor: not-allowed !important;
}

/* Base DatePicker Styles */
.datepicker {
    position: relative;
    width: 100%;
}

.datepicker-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.datepicker-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    background-color: white;
}

.datepicker-trigger {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    transition: color 0.2s ease-in-out;
    padding: 0.25rem;
    border-radius: 0.25rem;
}

.datepicker-trigger:hover {
    color: #374151;
    background-color: #f3f4f6;
}

/* Variant Styles */
.datepicker[data-variant="flat"] .datepicker-input {
    background-color: #f8fafc;
    border: 1px solid transparent;
}

.datepicker[data-variant="flat"] .datepicker-input:focus {
    outline: none;
    background-color: white;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.datepicker[data-variant="bordered"] .datepicker-input {
    border: 2px solid #e5e7eb;
    background-color: white;
}

.datepicker[data-variant="bordered"] .datepicker-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.datepicker[data-variant="faded"] .datepicker-input {
    background-color: #f1f5f9;
    border: 1px solid #cbd5e1;
}

.datepicker[data-variant="faded"] .datepicker-input:focus {
    outline: none;
    background-color: white;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.datepicker[data-variant="underlined"] .datepicker-input {
    background-color: transparent;
    border: none;
    border-bottom: 2px solid #e5e7eb;
    border-radius: 0;
    padding-left: 0;
}

.datepicker[data-variant="underlined"] .datepicker-input:focus {
    outline: none;
    border-bottom-color: #3b82f6;
    box-shadow: 0 2px 0 0 rgba(59, 130, 246, 0.1);
}

/* Size Styles - Simplified to only lg size */
.datepicker[data-size="lg"] .datepicker-input {
    padding: 1rem 3.5rem 1rem 1.25rem;
    font-size: 1rem;
}

.datepicker[data-size="lg"] .datepicker-trigger {
    right: 1rem;
}

/* Color Styles - Simplified to primary only */
.datepicker[data-color="primary"] .datepicker-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Calendar Popup */
.datepicker-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease-in-out;
}

.datepicker-popup.show {
    opacity: 1;
    visibility: visible;
}

.datepicker-popup-content {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    padding: 1.5rem;
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.95);
    transition: transform 0.2s ease-in-out;
}

.datepicker-popup.show .datepicker-popup-content {
    transform: scale(1);
}

/* Calendar Header */
.calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.calendar-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
}

.calendar-nav-btn {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 0.375rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.calendar-nav-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

/* Calendar Grid */
.calendar-grid {
    margin-bottom: 1rem;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.calendar-weekday {
    text-align: center;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    padding: 0.5rem;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border: 1px solid transparent;
}

.calendar-day:hover {
    background-color: #f3f4f6;
}

.calendar-day.selected {
    background-color: #3b82f6;
    color: white;
}

.calendar-day.today {
    border-color: #3b82f6;
    color: #3b82f6;
    font-weight: 600;
}

.calendar-day.other-month {
    color: #d1d5db;
}

.calendar-day.disabled {
    color: #d1d5db;
    cursor: not-allowed;
    background-color: #f9fafb;
}

.calendar-day.disabled:hover {
    background-color: #f9fafb;
}

/* Removed redundant time picker CSS */

/* Time Slots Container - Updated for Flatpickr integration */
.time-slots-container {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
    margin-top: 1rem;
}

.time-slots-container h4 {
    margin-bottom: 0.75rem;
}

.time-slots-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    max-height: 200px;
    overflow-y: auto;
}

/* Legacy time-slots class for backward compatibility */
.time-slots {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
    margin-top: 1rem;
}

.time-slots-header {
    margin-bottom: 0.75rem;
}

.time-slot {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    text-align: center;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background-color: white;
}

.time-slot:hover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.time-slot.selected {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.time-slot.disabled {
    background-color: #f9fafb;
    color: #d1d5db;
    cursor: not-allowed;
    border-color: #f3f4f6;
}

.time-slot.disabled:hover {
    background-color: #f9fafb;
    border-color: #f3f4f6;
}

/* Calendar Footer */
.calendar-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.calendar-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border: 1px solid transparent;
}

.calendar-btn-secondary {
    background-color: white;
    color: #6b7280;
    border-color: #d1d5db;
}

.calendar-btn-secondary:hover {
    background-color: #f9fafb;
    color: #374151;
}

.calendar-btn-primary {
    background-color: #3b82f6;
    color: white;
}

.calendar-btn-primary:hover {
    background-color: #2563eb;
}

/* Responsive Design */
@media (max-width: 640px) {
    .datepicker-popup-content {
        width: 95%;
        padding: 1rem;
    }

    .time-slots-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Animation Classes - Removed unused fade animations */
