<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>

    <wl-cordy-box-cart>
        <div class="product">
            <!-- Object product -->
            <add-to-cart-button>Click</add-to-cart-button>
        </div>
    </wl-cordy-box-cart>

    <script>
        class AddToCartButton extends HTMLElement {
            constructor() {
                super();
                // Add a click event listener
                this.addEventListener('click', this.handleClick);
            }

            handleClick() {
                // Create a sample product object
                const product = {
                    id: 1,
                    name: 'Sample Product',
                    price: 100
                };

                // Find the parent wl-cordy-box-cart element
                const cartBox = this.closest('wl-cordy-box-cart');

                // If the cartBox exists, push the product into the carts array
                if (cartBox) {
                    cartBox.addProductToCart(product);
                }
            }
        }

        class WLCordyBoxCart extends HTMLElement {
            constructor() {
                super();
                this.carts = [];
            }

            // Method to add a product to the carts array
            addProductToCart(product) {
                this.carts.push(product);
                console.log('Product added to cart:', product);
                console.log('Current cart:', this.carts);
            }
        }

        // Define custom elements
        customElements.define('add-to-cart-button', AddToCartButton);
        customElements.define('wl-cordy-box-cart', WLCordyBoxCart);
    </script>
</body>

</html>