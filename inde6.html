<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <validate-form>
        <form action="">
            <validate-element email>
                <input type="text" name="name2">
                <span data-error-msg></span>
            </validate-element>

            <div>
                <input type="text" name="name3">
            </div>

            <validate-element required>
                <input type="text" name="name4">
                <span data-error-msg></span>
            </validate-element>

            <validate-element required>
                <input type="text" name="name5">
                <span data-error-msg></span>
            </validate-element>

            <button type="submit" disabled>Submit</button>
        </form>
    </validate-form>

    <script>

        class ValidateElement extends HTMLElement {
            constructor() {
                super();
                // required, email
            }
        }
        customElements.define('validate-element', ValidateElement)

        class ValidateForm extends HTMLElement {
            constructor() {
                super();
                this.validate = {
                    'email': /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                    'required': /\S+/
                }
                this.form = document.querySelector('form');
                this.btnSubmit = this.form.querySelector('button');
                const { inValidObj, keyValidate } = this.getKeyAttribute();
                this.inValidObj = inValidObj;
                this.keyValidate = keyValidate;

                this.form.addEventListener('change', (e) => {
                    this.onFormChange(e)
                })
            }

            getKeyAttribute() {
                const keyValidate = {};

                Array.from(document.querySelectorAll('validate-element')).forEach((el) => {
                    const name = el.querySelector('input,select,checkbox,radio').getAttribute('name');
                    for (let i = 0; i < el.attributes.length; i++) {
                        keyValidate[name] = el.attributes[i].name
                    }
                });

                const inValidObj = Object.keys(keyValidate).reduce((init, value) => {
                    init[value] = false;
                    return init;
                }, {});

                return { inValidObj, keyValidate };
            }

            onFormChange(e) {
                const { value, name } = e.target;
                Object.values(this.keyValidate).forEach((validate, index) => {
                    if (name == Object.keys(this.keyValidate)[index]) {
                        const isInvalid = this.validate[validate].test(value);
                        this.inValidObj[name] = isInvalid;
                    }
                });
                const isDisabled = Object.values(this.inValidObj).every(errBoolean => errBoolean);
                if (isDisabled) {
                    this.btnSubmit.removeAttribute('disabled');
                } else {
                    this.btnSubmit.setAttribute('disabled', true);
                }
            }
        }
        customElements.define('validate-form', ValidateForm)

        // const validate = {
        //     'email': /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        //     'required': /\S+/
        // };

        // const inputValidateEmail = ['name2'];
        // const inputValidateRequired = ['name4', 'name5'];

        // const arrValidate = [...inputValidateEmail, ...inputValidateRequired];

        // const inValidObj = arrValidate.reduce((init, value) => {
        //     init[value] = false;
        //     return init;
        // }, {});

        // document.querySelector('form').addEventListener('change', (e) => {
        //     const { value, name } = e.target;

        //     if (inputValidateEmail.includes(name)) {
        //         const isInvalid = validate.email.test(value);
        //         if (!isInvalid) {
        //             e.target.nextElementSibling.innerHTML = 'Email không đúng định dạng!';
        //         } else {
        //             e.target.nextElementSibling.innerHTML = '';
        //         }
        //         inValidObj[name] = isInvalid;
        //     }

        //     if (inputValidateRequired.includes(name)) {
        //         const isInvalid = validate.required.test(value);
        //         if (!isInvalid) {
        //             e.target.nextElementSibling.innerHTML = 'Trường bắt buộc nhập';
        //         } else {
        //             e.target.nextElementSibling.innerHTML = '';
        //         }
        //         inValidObj[name] = isInvalid;
        //     }

        //     let isDisabled = true;
        //     isDisabled = Object.values(inValidObj).every(errBoolean => errBoolean);
        //     const btnSubmit = document.querySelector('button');
        //     if (isDisabled) {
        //         btnSubmit.removeAttribute('disabled');
        //     } else {
        //         btnSubmit.setAttribute('disabled', true);
        //     }
        // })
    </script>
</body>

</html>