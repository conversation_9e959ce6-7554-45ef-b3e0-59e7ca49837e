<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="index.js"></script>

</head>

<body>
    <validate-form>
        <form class="edit_form">
            <div data-required>
                <input type="text" name="email" id="email">
                <p data-cf-field="email" data-cf-error-action="show" class="field-error-message unico-m-form-invalid"
                    style="display: none;"></p>
            </div>

            <button>Click</button>
        </form>
    </validate-form>
    <script>
        class ValidateForm extends ValidateFormElement {
            constructor() {
                super();
                this.btn = this.querySelector('button');
                this.btn.addEventListener('click', (e) => {
                    e.preventDefault();

                    this.currentFormId = 'edit_form';
                    this.submit(e);
                });
            }
            handleForm() {
                console.log("ahahahah")
            }
            submit(e) {
                e.preventDefault();
                this.handleSubmit(e, this.handleForm)
            }

        }

        customElements.define('validate-form', ValidateForm);
    </script>
</body>

</html>