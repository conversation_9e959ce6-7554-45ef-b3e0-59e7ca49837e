/* Clean Minimal Flatpickr Styles - Based on Image Design */

:root {
  /* Clean Color Palette */
  --clean-primary: #000000;
  --clean-primary-light: #333333;
  --clean-background: #ffffff;
  --clean-foreground: #000000;
  --clean-text-light: #666666;
  --clean-text-lighter: #999999;
  --clean-border: #e5e5e5;
  --clean-border-light: #f0f0f0;
  --clean-hover: #f8f8f8;

  --clean-radius: 16px;
  --clean-radius-sm: 8px;

  --clean-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --clean-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* Flatpickr Calendar Container */
.flatpickr-calendar.clean-theme {
  background: var(--clean-background) !important;
  border: 1px solid var(--clean-border) !important;
  border-radius: var(--clean-radius) !important;
  box-shadow: var(--clean-shadow) !important;
  padding: 24px !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: var(--clean-foreground) !important;
  width: 320px !important;
  animation: cleanSlideIn 0.2s ease-out !important;
}

/* Calendar Header */
.flatpickr-calendar.clean-theme .flatpickr-months {
  padding-bottom: 16px !important;
  margin-bottom: 16px !important;
  border-bottom: none !important;
}

.flatpickr-calendar.clean-theme .flatpickr-month {
  background: transparent !important;
  color: var(--clean-foreground) !important;
  height: auto !important;
}

.flatpickr-calendar.clean-theme .flatpickr-current-month {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: var(--clean-foreground) !important;
  padding: 0 !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flatpickr-calendar.clean-theme .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: transparent !important;
  border: none !important;
  color: var(--clean-foreground) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  padding: 0 !important;
  margin: 0 8px !important;
  cursor: pointer !important;
}

.flatpickr-calendar.clean-theme .numInputWrapper {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  width: auto !important;
}

.flatpickr-calendar.clean-theme .numInputWrapper input {
  color: var(--clean-foreground) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  background: transparent !important;
  border: none !important;
  text-align: center !important;
  width: 60px !important;
}

/* Navigation Arrows */
.flatpickr-calendar.clean-theme .flatpickr-prev-month,
.flatpickr-calendar.clean-theme .flatpickr-next-month {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  background: transparent !important;
  border: none !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--clean-text-light) !important;
  cursor: pointer !important;
  top: 16px !important;
}

.flatpickr-calendar.clean-theme .flatpickr-prev-month:hover,
.flatpickr-calendar.clean-theme .flatpickr-next-month:hover {
  background: var(--clean-hover) !important;
  color: var(--clean-foreground) !important;
}

.flatpickr-calendar.clean-theme .flatpickr-prev-month svg,
.flatpickr-calendar.clean-theme .flatpickr-next-month svg {
  width: 16px !important;
  height: 16px !important;
  fill: currentColor !important;
}

/* Weekdays */
.flatpickr-calendar.clean-theme .flatpickr-weekdays {
  background: transparent !important;
  margin: 12px 0 8px 0 !important;
  padding: 0 !important;
}

.flatpickr-calendar.clean-theme .flatpickr-weekday {
  background: transparent !important;
  color: var(--clean-text-light) !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: 8px 0 !important;
  text-align: center !important;
  border-radius: 0 !important;
  margin: 0 !important;
  width: 40px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Calendar Days */
.flatpickr-calendar.clean-theme .flatpickr-days {
  width: 100% !important;
  padding: 0 !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day {
  background: transparent !important;
  border: none !important;
  border-radius: 50% !important;
  color: var(--clean-foreground) !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  height: 40px !important;
  width: 40px !important;
  line-height: 40px !important;
  margin: 2px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day:hover {
  background: var(--clean-hover) !important;
  color: var(--clean-foreground) !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day.selected {
  background: var(--clean-primary) !important;
  color: white !important;
  font-weight: 500 !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day.today {
  background: var(--clean-border-light) !important;
  color: var(--clean-foreground) !important;
  font-weight: 500 !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day.today:hover {
  background: var(--clean-hover) !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day.today.selected {
  background: var(--clean-primary) !important;
  color: white !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day.prevMonthDay,
.flatpickr-calendar.clean-theme .flatpickr-day.nextMonthDay {
  color: var(--clean-text-lighter) !important;
  opacity: 0.5 !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day.disabled {
  color: var(--clean-text-lighter) !important;
  cursor: not-allowed !important;
  background: transparent !important;
  opacity: 0.3 !important;
}

.flatpickr-calendar.clean-theme .flatpickr-day.disabled:hover {
  background: transparent !important;
  color: var(--clean-text-lighter) !important;
  cursor: not-allowed !important;
}

/* Input Field Styling */
.clean-datepicker-input {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.4;
  color: var(--clean-foreground);
  background: var(--clean-background);
  border: 1px solid var(--clean-border);
  border-radius: var(--clean-radius-sm);
  transition: all 0.2s ease;
  box-sizing: border-box;
  font-weight: 400;
}

.clean-datepicker-input:hover {
  border-color: var(--clean-text-light);
}

.clean-datepicker-input:focus {
  outline: none;
  border-color: var(--clean-primary);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.clean-datepicker-input::placeholder {
  color: var(--clean-text-lighter);
  font-weight: 400;
}

/* Animation */
.flatpickr-calendar.clean-theme.open {
  animation: cleanSlideIn 0.2s ease-out;
}

@keyframes cleanSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
