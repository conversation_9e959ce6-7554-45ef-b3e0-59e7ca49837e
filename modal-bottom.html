<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<style>
    html,
    body {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
    }

    body.bottom-modal::after {
        content: "";
        position: fixed;
        z-index: 1;
        background: #c2c2c261;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    }

    bottom-modal .modal-content {
        opacity: 0;
        visibility: hidden;
    }

    bottom-modal .modal-content.active {
        opacity: 1;
        visibility: visible;
        position: fixed;
        z-index: 2;
        bottom: 0;
        width: 100%;
        margin: 0;
        padding: 0;
        left: 0;
        background: #fff;
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
        animation-name: showModal;
        animation-duration: 0.5s;
    }

    bottom-modal .modal-content.un_active {
        animation-name: closeModal;
        animation-duration: 0.5s;
    }

    @keyframes showModal {
        0% {
            transform: translateY(500px);
        }

        100% {
            transform: translateY(0);
        }
    }

    @keyframes closeModal {
        0% {
            transform: translateY(0);
        }

        100% {
            transform: translateY(500px);
            opacity: 0;
            visibility: hidden;
        }
    }

    bottom-modal .modal-content h2.title {
        text-align: center;
        margin: 0;
        padding: 15px;
    }

    bottom-modal ul {
        margin: 0;
        padding: 0;
        background: #eeeeee;
        padding-top: 20px;
    }

    bottom-modal ul li {
        list-style: none;
        text-align: center;
        padding: 10px;
        font-weight: bold;
        font-size: 12px;
    }

    bottom-modal ul li.active {
        background: #fff;
    }

    bottom-modal .modal-content-header {
        border-bottom: 1px solid #ccc;
        display: flex;
        align-items: center;
        position: relative;
    }

    bottom-modal .modal-content-header__title {
        flex: 1;
        text-align: center;
    }

    bottom-modal .modal-content-header__close {
        position: absolute;
        right: 10px;
    }
</style>

<body>
    <bottom-modal>
        <open-title>
            <h3>open modal</h3>
        </open-title>
        <div class="modal-content">
            <div class="modal-content-header">
                <h2 class="modal-content-header__title">title</h2>
                <span class="modal-content-header__close">close</span>
            </div>
            <ul>
                <li>1</li>
                <li>2</li>
                <li>3</li>
                <li>4</li>
            </ul>
        </div>
    </bottom-modal>
    <script>
        class OpenTitle extends HTMLElement {
            constructor() {
                super();
            }
        }
        customElements.define('open-title', OpenTitle);

        class BottomModal extends HTMLElement {
            constructor() {
                super();
                this.isOpen = false;
                this.bottomModal = document.querySelector('bottom-modal');
                this.modalContent = this.bottomModal.querySelector('.modal-content');
                this.body = document.querySelector('body');
                document.querySelector('open-title').addEventListener('click', () => this.toggleModal());
                document.querySelector('.modal-content-header__close').addEventListener('click', () => this.toggleModal());
            }

            toggleModal() {
                this.isOpen = !this.isOpen;
                if (this.isOpen) {
                    this.modalContent.classList.add('active');

                    this.body.classList.add('bottom-modal');
                } else {
                    this.body.classList.remove('bottom-modal');
                    this.modalContent.classList.add('un_active');

                    setTimeout(() => {
                        this.modalContent.classList.remove('un_active');
                        this.modalContent.classList.remove('active');
                    }, 500);

                }
            }
        }
        customElements.define('bottom-modal', BottomModal);
    </script>
</body>

</html>