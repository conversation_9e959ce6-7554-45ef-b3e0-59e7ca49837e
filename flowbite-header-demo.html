<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flowbite Datepicker - Header Customization</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <!-- Flowbite Flatpickr Theme -->
    <link rel="stylesheet" href="./flatpickr-flowbite.css">
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div class="min-h-screen py-12 px-4">
        <div class="max-w-2xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Datepicker Header Customization
                </h1>
                <p class="text-gray-600 dark:text-gray-300">
                    Different ways to display month and year in the datepicker header
                </p>
            </div>

            <!-- Demo Examples -->
            <div class="space-y-8">
                
                <!-- Style 1: Default Enhanced -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Style 1: Enhanced Default Header
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Month dropdown + Year input with better styling
                    </p>
                    
                    <div class="flowbite-datepicker-container">
                        <div class="flowbite-datepicker-icon">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input type="text" id="style1-datepicker" class="flowbite-datepicker-input" placeholder="Select date">
                    </div>
                </div>

                <!-- Style 2: Custom Gradient Header -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Style 2: Custom Gradient Header
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Beautiful gradient header with full month name and year
                    </p>
                    
                    <div class="flowbite-datepicker-container">
                        <div class="flowbite-datepicker-icon">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input type="text" id="style2-datepicker" class="flowbite-datepicker-input" placeholder="Select date">
                    </div>
                </div>

                <!-- Style 3: Minimal Text Header -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Style 3: Minimal Text Header
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Clean text-only header with month abbreviation
                    </p>
                    
                    <div class="flowbite-datepicker-container">
                        <div class="flowbite-datepicker-icon">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input type="text" id="style3-datepicker" class="flowbite-datepicker-input" placeholder="Select date">
                    </div>
                </div>

                <!-- Style 4: Badge Style Header -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Style 4: Badge Style Header
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Month and year displayed as separate badges
                    </p>
                    
                    <div class="flowbite-datepicker-container">
                        <div class="flowbite-datepicker-icon">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input type="text" id="style4-datepicker" class="flowbite-datepicker-input" placeholder="Select date">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flatpickr JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <script>
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        const monthNamesShort = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ];

        // Style 1: Enhanced Default Header
        flatpickr("#style1-datepicker", {
            dateFormat: "Y-m-d",
            monthSelectorType: 'dropdown',
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
            }
        });

        // Style 2: Custom Gradient Header
        flatpickr("#style2-datepicker", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
                
                // Create custom gradient header
                const customHeader = document.createElement('div');
                customHeader.className = 'flatpickr-custom-header';
                
                const updateHeader = () => {
                    const monthName = monthNames[instance.currentMonth];
                    const year = instance.currentYear;
                    customHeader.textContent = `${monthName} ${year}`;
                };
                
                updateHeader();
                instance.calendarContainer.insertBefore(customHeader, instance.calendarContainer.firstChild);
                
                // Update header when month/year changes
                instance.config.onMonthChange.push(updateHeader);
                instance.config.onYearChange.push(updateHeader);
            }
        });

        // Style 3: Minimal Text Header
        flatpickr("#style3-datepicker", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
                
                // Hide default header
                const monthsContainer = instance.monthNav;
                if (monthsContainer) {
                    monthsContainer.style.display = 'none';
                }
                
                // Create minimal text header
                const textHeader = document.createElement('div');
                textHeader.style.cssText = `
                    text-align: center;
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--flowbite-gray-700);
                    padding: 0.75rem 0;
                    margin-bottom: 0.5rem;
                    border-bottom: 1px solid var(--flowbite-gray-200);
                `;
                
                const updateTextHeader = () => {
                    const monthName = monthNamesShort[instance.currentMonth];
                    const year = instance.currentYear;
                    textHeader.textContent = `${monthName} ${year}`;
                };
                
                updateTextHeader();
                instance.calendarContainer.insertBefore(textHeader, instance.daysContainer.parentNode);
                
                // Update header when month/year changes
                instance.config.onMonthChange.push(updateTextHeader);
                instance.config.onYearChange.push(updateTextHeader);
            }
        });

        // Style 4: Badge Style Header
        flatpickr("#style4-datepicker", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
                
                // Hide default header
                const monthsContainer = instance.monthNav;
                if (monthsContainer) {
                    monthsContainer.style.display = 'none';
                }
                
                // Create badge header container
                const badgeHeader = document.createElement('div');
                badgeHeader.style.cssText = `
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 0;
                    margin-bottom: 0.5rem;
                `;
                
                const monthBadge = document.createElement('span');
                monthBadge.style.cssText = `
                    background: var(--flowbite-primary);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.875rem;
                    font-weight: 500;
                `;
                
                const yearBadge = document.createElement('span');
                yearBadge.style.cssText = `
                    background: var(--flowbite-gray-100);
                    color: var(--flowbite-gray-700);
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.875rem;
                    font-weight: 500;
                `;
                
                const updateBadgeHeader = () => {
                    const monthName = monthNamesShort[instance.currentMonth];
                    const year = instance.currentYear;
                    monthBadge.textContent = monthName;
                    yearBadge.textContent = year;
                };
                
                updateBadgeHeader();
                badgeHeader.appendChild(monthBadge);
                badgeHeader.appendChild(yearBadge);
                instance.calendarContainer.insertBefore(badgeHeader, instance.daysContainer.parentNode);
                
                // Update header when month/year changes
                instance.config.onMonthChange.push(updateBadgeHeader);
                instance.config.onYearChange.push(updateBadgeHeader);
            }
        });
    </script>
</body>
</html>
