<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Facebook-style Comment</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background: #f0f2f5;
    }

    .comment-box {
      background: #fff;
      padding: 10px 15px;
      border-radius: 8px;
      margin-bottom: 10px;
    }

    .comment {
      margin-top: 10px;
    }

    .comment-content {
      margin-bottom: 4px;
    }

    .comment-actions {
      font-size: 0.85em;
      color: #65676b;
    }

    .comment-actions span {
      margin-right: 10px;
      cursor: pointer;
    }

    .reply-box {
      margin-left: 40px;
      margin-top: 8px;
    }

    .reply-input {
      background: #f0f2f5;
      border: none;
      border-radius: 16px;
      padding: 8px 12px;
      width: 80%;
      font-size: 0.95em;
      outline: none;
    }

    .submit-btn {
      margin-top: 5px;
      margin-left: 40px;
    }

    .reply-form {
      margin-top: 10px;
    }
  </style>
</head>
<body>

<h2>Facebook-style Comment Thread</h2>

<div id="comment-thread"></div>

<!-- Reply form (floating, reusable) -->
<div id="reply-form" class="reply-form" style="display: none;">
  <input type="text" class="reply-input" placeholder="Write a reply...">
  <button class="submit-btn">Submit</button>
</div>

<script>
  const data = [
    {
      id: "1",
      author: "User A",
      content: "This is a top-level comment.",
      children: [
        {
          id: "1.1",
          author: "User B",
          content: "Reply to User A",
          children: []
        }
      ]
    },
    {
      id: "2",
      author: "User C",
      content: "Another top-level comment.",
      children: []
    }
  ];

  const container = document.getElementById("comment-thread");
  const replyForm = document.getElementById("reply-form");
  const replyInput = replyForm.querySelector("input");
  const submitBtn = replyForm.querySelector("button");

  let replyingTo = null;

  function renderComments(comments, parentElement, indent = 0) {
    comments.forEach(comment => {
      const div = document.createElement("div");
      div.className = "comment-box";
      div.style.marginLeft = indent + "px";

      div.innerHTML = `
        <div class="comment-content"><strong>${comment.author}</strong>: ${comment.content}</div>
        <div class="comment-actions">
          <span class="reply-btn" data-id="${comment.id}">Reply</span>
        </div>
      `;

      parentElement.appendChild(div);

      div.querySelector('.reply-btn').addEventListener('click', () => {
        replyingTo = comment;
        replyInput.value = '';
        div.appendChild(replyForm);
        replyForm.style.display = 'block';
        replyInput.focus();
      });

      if (comment.children && comment.children.length > 0) {
        renderComments(comment.children, parentElement, indent + 40);
      }
    });
  }

  function refreshComments() {
    container.innerHTML = '';
    renderComments(data, container);
  }

 submitBtn.addEventListener('click', () => {
  const text = replyInput.value.trim();
  if (!text || !replyingTo) return;

  // Tạo id mới kiểu 'parentId.nextIndex'
  const parent = replyingTo;
  const children = parent.children;
  let newIndex = 1;
  if (children.length > 0) {
    // Lấy id cuối cùng con, ví dụ "1.1" => 1
    const lastId = children[children.length - 1].id;
    const parts = lastId.split('.');
    newIndex = parseInt(parts[parts.length - 1]) + 1;
  }

  const newId = parent.id + '.' + newIndex;

  const newComment = {
    id: newId,
    author: "You",
    content: text,
    children: []
  };

  // Thêm comment con mới
  parent.children.push(newComment);

  // Rerender
  refreshComments();
  replyForm.style.display = 'none';
  replyingTo = null;
});

  // Initial render
  refreshComments();
</script>

</body>
</html>
