<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Datepicker Demo</title>
    
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <!-- Custom Clean Theme CSS -->
    <link rel="stylesheet" href="flatpickr-heroui.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .demo-container {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            max-width: 400px;
            width: 100%;
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: 600;
            color: #000;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .demo-subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 32px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #000;
            margin-bottom: 8px;
        }
        
        .demo-note {
            font-size: 12px;
            color: #999;
            margin-top: 16px;
            text-align: center;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Clean Datepicker</h1>
        <p class="demo-subtitle">Minimal design inspired by the provided image</p>
        
        <div class="form-group">
            <label class="form-label" for="datepicker1">Select Date</label>
            <input type="text" id="datepicker1" class="clean-datepicker-input" placeholder="Choose a date...">
        </div>
        
        <div class="form-group">
            <label class="form-label" for="datepicker2">Select Date Range</label>
            <input type="text" id="datepicker2" class="clean-datepicker-input" placeholder="Choose date range...">
        </div>
        
        <p class="demo-note">
            Click on the input fields above to open the clean-themed datepicker.
            The design matches the minimal style from your reference image.
        </p>
    </div>

    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <script>
        // Initialize single date picker
        flatpickr("#datepicker1", {
            theme: "clean-theme",
            dateFormat: "F j, Y",
            defaultDate: "2022-02-10", // Set to match the image
            onReady: function(selectedDates, dateStr, instance) {
                // Add clean theme class to the calendar
                instance.calendarContainer.classList.add('clean-theme');
            }
        });
        
        // Initialize date range picker
        flatpickr("#datepicker2", {
            theme: "clean-theme", 
            mode: "range",
            dateFormat: "F j, Y",
            onReady: function(selectedDates, dateStr, instance) {
                // Add clean theme class to the calendar
                instance.calendarContainer.classList.add('clean-theme');
            }
        });
    </script>
</body>
</html>
