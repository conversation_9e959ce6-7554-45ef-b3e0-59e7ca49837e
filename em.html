<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注文詳細</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f4f7f6;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            background-color: #eef2f5;
            padding: 15px 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .section-header span {
            font-weight: normal;
            color: #555;
            font-size: 0.95em;
        }

        .section-content {
            margin-bottom: 30px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: 15px 0;
            margin-bottom: 20px;
        }

        .info-grid div {
            padding: 5px 0;
        }

        .info-label {
            font-weight: bold;
            color: #555;
        }

        .product-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            margin-bottom: 30px;
        }

        .product-table th, .product-table td {
            border-bottom: 1px solid #eee;
            padding: 12px 10px;
            text-align: left;
        }

        .product-table th {
            background-color: #f9fbfb;
            font-weight: bold;
            color: #555;
        }

        .product-table td:nth-child(2), /* 価格 */
        .product-table td:nth-child(3), /* 数量 */
        .product-table td:nth-child(4)  /* 合計 */ {
            text-align: right;
            white-space: nowrap; /* Prevents wrapping for prices/quantities */
        }
        
        .product-table th:nth-child(2),
        .product-table th:nth-child(3),
        .product-table th:nth-child(4) {
            text-align: right;
        }

        .detail-item {
            display: flex;
            margin-bottom: 10px;
        }

        .detail-item dt {
            font-weight: bold;
            width: 100px;
            color: #555;
        }

        .detail-item dd {
            margin-left: 10px;
            flex-grow: 1;
        }

        .tracking-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }

        .tracking-number {
            font-size: 1.1em;
            font-weight: bold;
            color: #007bff;
            text-decoration: none;
        }

        .tracking-number:hover {
            text-decoration: underline;
        }

        .tracking-button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            font-size: 1.2em;
            transition: background-color 0.2s ease-in-out;
        }

        .tracking-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="section-header">
            <div>お届け先 1： 田中 太郎 様 <span>(〒107-0062 東京都港区南青山 2 丁目 2 - 17 川上ビル 6 F)</span></div>
        </div>

        <div class="section-content">
            <div class="info-grid">
                <div class="info-label">お名前</div>
                <div>田中 太郎 様</div>
                <div class="info-label">住所</div>
                <div>
                    〒107-0062<br>
                    東京都港区南青山 2 丁目 22 - 17 川上ビル 6 F
                </div>
            </div>
        </div>

        <table class="product-table">
            <thead>
                <tr>
                    <th>商品</th>
                    <th>価格</th>
                    <th>数量</th>
                    <th>合計</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>国内産黒毛和牛 お惣菜用切り落とし</td>
                    <td>¥50,000</td>
                    <td>2</td>
                    <td>¥100,000</td>
                </tr>
                <tr>
                    <td>ストレッチスウェットオーバーサイズプルオーバー</td>
                    <td>¥100,000</td>
                    <td>2</td>
                    <td>¥200,000</td>
                </tr>
            </tbody>
        </table>

        <dl class="section-content">
            <div class="detail-item">
                <dt>ラッピング</dt>
                <dd>ホワイトデー仕様</dd>
            </div>
            <div class="detail-item">
                <dt>のし</dt>
                <dd>なし</dd>
            </div>
            <div class="detail-item">
                <dt>メッセージ</dt>
                <dd>ありがとう！</dd>
            </div>
        </dl>

        <hr style="border: 0; border-top: 1px solid #eee; margin: 30px 0;">

        <dl class="section-content">
            <div class="detail-item">
                <dt>配送希望日時</dt>
                <dd>2025/04/15 午前中</dd>
            </div>
            <div class="detail-item">
                <dt>配送業者</dt>
                <dd>ヤマト運輸</dd>
            </div>
            <div class="detail-item">
                <dt>追跡番号</dt>
                <dd>123456789</dd>
            </div>
        </dl>
    </div>
</body>
</html>