<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <title>Kết nối Telegram</title>
</head>
<body>
  <h2>🔗 Kết nối tài khoản Telegram</h2>

  <!-- <PERSON><PERSON>t Telegram Login -->
  <script async src="https://telegram.org/js/telegram-widget.js?7"
          data-telegram-login="your_bot_username"  
          data-size="large"
          data-userpic="false"
          data-request-access="write"
          data-onauth="onTelegramAuth(user)">
  </script>

  <pre id="output"></pre>

  <!-- JS xử lý sau khi Telegram gửi user -->
  <script>
    function onTelegramAuth(user) {
      console.log('User info:', user);
      document.getElementById('output').textContent =
        'Telegram user đã liên kết:\n\n' + JSON.stringify(user, null, 2);

      // Gửi user về server nếu muốn li<PERSON><PERSON> kết DB
      // fetch('/api/link-telegram', {
      //   method: 'POST',
      //   headers: {'Content-Type': 'application/json'},
      //   body: JSON.stringify(user)
      // });
    }
  </script>
</body>
</html>
