<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>HeroUI-Style Modern Datepicker</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="datepicker-styles.css">

  <!-- Google Fonts for better typography -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Flatpickr CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">


</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <h1 class="main-title">HeroUI-Style Datepicker</h1>
      <p class="subtitle">Modern, accessible date selection with beautiful design</p>
    </div>

    <!-- Main Card -->
    <div class="main-card">
      <!-- Basic Datepicker -->
      <div class="datepicker-section">
        <div class="section-header">
          <h2 class="section-title">Select Date</h2>
          <p class="section-description">Choose your preferred date from the calendar</p>
        </div>

        <!-- Month/Year Header -->
        <div class="month-year-header">
          <div class="month-year-display" id="month-year-display">
            <!-- Will be populated by JavaScript -->
          </div>
          <div class="nav-buttons">
            <button class="nav-button" id="prev-month-btn">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            <button class="nav-button" id="next-month-btn">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>

        <div class="input-wrapper" id="datepicker-wrapper">
          <label class="floating-label" for="datepicker">Select a date</label>
          <input
            id="datepicker"
            type="text"
            class="modern-input"
            placeholder="Choose date..."
            readonly
          >
          <svg class="calendar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>



  <!-- Flatpickr JS -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

  <script>
    // Enhanced input interaction handlers
    function setupInputWrapper(wrapperId, inputId) {
      const wrapper = document.getElementById(wrapperId);
      const input = document.getElementById(inputId);
      const label = wrapper.querySelector('.floating-label');

      // Handle focus states
      input.addEventListener('focus', () => {
        wrapper.classList.add('focused');
        label.classList.add('active');
      });

      input.addEventListener('blur', () => {
        wrapper.classList.remove('focused');
        if (!input.value) {
          label.classList.remove('active');
        }
      });

      // Handle input changes
      input.addEventListener('input', () => {
        if (input.value) {
          label.classList.add('active');
        } else {
          label.classList.remove('active');
        }
      });

      // Initial state
      if (input.value) {
        label.classList.add('active');
      }
    }

    // Initialize input wrapper for the single datepicker
    setupInputWrapper('datepicker-wrapper', 'datepicker');

    // Global variables for date management
    let currentDisplayDate = new Date();
    let flatpickrInstance;
    const today = new Date();

    // Normalize today's date to start of day for accurate comparison
    const todayNormalized = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // Month names for display
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    // Function to check if a date is before today
    function isDateBeforeToday(date) {
      const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      return checkDate < todayNormalized;
    }

    // Function to update month/year display and button visibility
    function updateMonthYearDisplay() {
      const monthYearDisplay = document.getElementById('month-year-display');
      const prevButton = document.getElementById('prev-month-btn');

      // Update display text
      monthYearDisplay.textContent = `${monthNames[currentDisplayDate.getMonth()]} ${currentDisplayDate.getFullYear()}`;

      // Show/hide custom prev button based on current month
      const isCurrentMonth = currentDisplayDate.getMonth() === today.getMonth() &&
                             currentDisplayDate.getFullYear() === today.getFullYear();

      if (isCurrentMonth) {
        prevButton.style.display = 'none';
      } else {
        prevButton.style.display = 'flex';
      }

      // Control Flatpickr's built-in prev button visibility
      updateFlatpickrPrevButton();
    }

    // Function to control Flatpickr's built-in previous button
    function updateFlatpickrPrevButton() {
      const flatpickrPrevButton = document.querySelector('.flatpickr-prev-month');

      if (flatpickrPrevButton) {
        const isCurrentMonth = currentDisplayDate.getMonth() === today.getMonth() &&
                               currentDisplayDate.getFullYear() === today.getFullYear();

        if (isCurrentMonth) {
          flatpickrPrevButton.classList.add('hidden');
        } else {
          flatpickrPrevButton.classList.remove('hidden');
        }
      }
    }

    // Function to navigate months
    function navigateMonth(direction) {
      const newDate = new Date(currentDisplayDate);
      newDate.setMonth(newDate.getMonth() + direction);

      // Don't allow navigation to months before current month
      if (direction < 0) {
        const isBeforeCurrentMonth = newDate.getMonth() < today.getMonth() &&
                                   newDate.getFullYear() <= today.getFullYear();
        if (isBeforeCurrentMonth) return;
      }

      currentDisplayDate = newDate;
      updateMonthYearDisplay();

      // Update Flatpickr to show the new month
      if (flatpickrInstance) {
        flatpickrInstance.changeMonth(direction, false);
      }
    }

    // Add event listeners for navigation buttons
    document.getElementById('prev-month-btn').addEventListener('click', () => {
      navigateMonth(-1);
    });

    document.getElementById('next-month-btn').addEventListener('click', () => {
      navigateMonth(1);
    });

    // Initialize Flatpickr instance with enhanced configuration
    flatpickrInstance = flatpickr("#datepicker", {
      dateFormat: "Y-m-d",
      altInput: true,
      altFormat: "F j, Y",
      disableMobile: true,
      animate: true,
      position: "below",
      defaultDate: "today",
      minDate: "today", // Disable all dates before today
      disable: [
        // Custom function to disable dates before today
        function(date) {
          // Use our improved date validation function
          return isDateBeforeToday(date);
        }
      ],
      onChange: function(selectedDates, dateStr, instance) {
        const wrapper = document.getElementById('datepicker-wrapper');
        const label = wrapper.querySelector('.floating-label');
        if (dateStr) {
          label.classList.add('active');
        }
      },
      onOpen: function(selectedDates, dateStr, instance) {
        const wrapper = document.getElementById('datepicker-wrapper');
        wrapper.classList.add('focused');

        // Update display when calendar opens
        currentDisplayDate = new Date(instance.currentYear, instance.currentMonth);
        updateMonthYearDisplay();

        // Small delay to ensure Flatpickr DOM is ready
        setTimeout(() => {
          updateFlatpickrPrevButton();
        }, 50);
      },
      onClose: function(selectedDates, dateStr, instance) {
        const wrapper = document.getElementById('datepicker-wrapper');
        wrapper.classList.remove('focused');
      },
      onReady: function(selectedDates, dateStr, instance) {
        // Set initial label state if there's a default date
        const wrapper = document.getElementById('datepicker-wrapper');
        const label = wrapper.querySelector('.floating-label');
        if (dateStr) {
          label.classList.add('active');
        }

        // Initialize month/year display
        currentDisplayDate = new Date(instance.currentYear, instance.currentMonth);
        updateMonthYearDisplay();

        // Small delay to ensure Flatpickr DOM is ready
        setTimeout(() => {
          updateFlatpickrPrevButton();
        }, 50);
      },
      onMonthChange: function(selectedDates, dateStr, instance) {
        // Update display when month changes via Flatpickr
        currentDisplayDate = new Date(instance.currentYear, instance.currentMonth);
        updateMonthYearDisplay();

        // Small delay to ensure Flatpickr DOM is updated
        setTimeout(() => {
          updateFlatpickrPrevButton();
        }, 50);
      }
    });

    // Initial setup
    updateMonthYearDisplay();

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
  </script>

</body>
</html>
