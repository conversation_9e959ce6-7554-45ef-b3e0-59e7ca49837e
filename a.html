<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Custom Collapse Elements</title>
  <style>
    body {
      font-family: sans-serif;
      padding: 2rem;
    }

    my-collapse {
      display: block;
      border: 1px solid #ccc;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 1rem;
    }

    collapse-header {
      display: block;
      background: #f0f0f0;
      padding: 1rem;
      cursor: pointer;
      font-weight: bold;
    }

    collapse-content {
      display: block;
      overflow: hidden;
      transition: height 0.3s ease;
    }
  </style>
</head>
<body>

<my-collapse>
  <collapse-header>Click để mở/đóng</collapse-header>
  <collapse-content>
    <p>Đ<PERSON>y là nội dung có thể thu gọn hoặc mở ra.</p>
    <p>Có thể chứa bất kỳ phần tử HTML nào.</p>
  </collapse-content>
</my-collapse>

<script>
  class CollapseHeader extends HTMLElement {
    connectedCallback() {
      this.addEventListener('click', () => {
        const parent = this.closest('my-collapse');
        if (parent) parent.toggle();
      });
    }
  }

  class CollapseContent extends HTMLElement {
    set expanded(value) {
      const content = this;
      if (value) {
        const height = this.scrollHeight;
        content.style.height = height + 'px';
        content.addEventListener('transitionend', () => {
          content.style.height = 'auto';
        }, { once: true });
      } else {
        const height = this.scrollHeight;
        content.style.height = height + 'px';
        requestAnimationFrame(() => {
          content.style.height = '0px';
        });
      }
    }
  }

  class MyCollapse extends HTMLElement {
    connectedCallback() {
      this._content = this.querySelector('collapse-content');
      this.setAttribute('collapsed', '');
      this._content.style.height = '0px';
    }

    toggle() {
      const isExpanded = this.hasAttribute('collapsed');
      this._content.expanded = isExpanded;
      if (isExpanded) {
        this.removeAttribute('collapsed');
      } else {
        this.setAttribute('collapsed', '');
      }
    }
  }

  customElements.define('collapse-header', CollapseHeader);
  customElements.define('collapse-content', CollapseContent);
  customElements.define('my-collapse', MyCollapse);
</script>

</body>
</html>
