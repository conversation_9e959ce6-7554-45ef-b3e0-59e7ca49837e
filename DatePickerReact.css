/* DatePicker React Component Styles */

/* Calendar Grid */
.calendar-grid {
  @apply mb-4;
}

.calendar-weekdays {
  @apply grid grid-cols-7 gap-1 mb-2;
}

.calendar-weekday {
  @apply text-center text-xs font-medium text-gray-500 py-2;
}

.calendar-days {
  @apply grid grid-cols-7 gap-1;
}

.calendar-day {
  @apply aspect-square flex items-center justify-center rounded-lg text-sm cursor-pointer transition-all duration-200 border border-transparent;
}

.calendar-day:hover:not(.disabled):not(.other-month) {
  @apply bg-gray-100;
}

.calendar-day.selected {
  @apply bg-blue-500 text-white border-blue-500;
}

.calendar-day.today {
  @apply border-blue-500 text-blue-600 font-semibold;
}

.calendar-day.other-month {
  @apply text-gray-300 cursor-default;
}

.calendar-day.disabled {
  @apply text-gray-300 cursor-not-allowed bg-gray-50;
}

.calendar-day.disabled:hover {
  @apply bg-gray-50;
}

/* Color Variants */
.datepicker[data-color="primary"] input:focus {
  @apply border-blue-500 ring-blue-100;
}

.datepicker[data-color="success"] input:focus {
  @apply border-green-500 ring-green-100;
}

.datepicker[data-color="success"] .calendar-day.selected {
  @apply bg-green-500 border-green-500;
}

.datepicker[data-color="success"] .calendar-day.today {
  @apply border-green-500 text-green-600;
}

.datepicker[data-color="warning"] input:focus {
  @apply border-yellow-500 ring-yellow-100;
}

.datepicker[data-color="warning"] .calendar-day.selected {
  @apply bg-yellow-500 border-yellow-500;
}

.datepicker[data-color="warning"] .calendar-day.today {
  @apply border-yellow-500 text-yellow-600;
}

.datepicker[data-color="danger"] input:focus {
  @apply border-red-500 ring-red-100;
}

.datepicker[data-color="danger"] .calendar-day.selected {
  @apply bg-red-500 border-red-500;
}

.datepicker[data-color="danger"] .calendar-day.today {
  @apply border-red-500 text-red-600;
}

/* Animation Classes */
.datepicker-popup-enter {
  @apply opacity-0 scale-95;
}

.datepicker-popup-enter-active {
  @apply opacity-100 scale-100 transition-all duration-200 ease-out;
}

.datepicker-popup-exit {
  @apply opacity-100 scale-100;
}

.datepicker-popup-exit-active {
  @apply opacity-0 scale-95 transition-all duration-200 ease-in;
}

/* Responsive Design */
@media (max-width: 640px) {
  .calendar-days {
    @apply gap-0.5;
  }
  
  .calendar-day {
    @apply text-xs;
  }
  
  .calendar-weekday {
    @apply text-xs py-1;
  }
}

/* Focus styles for accessibility */
.calendar-day:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Time slot grid responsive */
@media (max-width: 640px) {
  .time-slots-grid {
    @apply grid-cols-2;
  }
}
