<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flowbite Flatpickr Theme Demo</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <!-- Flowbite Flatpickr Theme -->
    <link rel="stylesheet" href="./flatpickr-flowbite.css">
    
    <style>
        /* Custom dark mode toggle */
        .dark-toggle {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <div class="min-h-screen py-12 px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    Flowbite Flatpickr Theme
                </h1>
                <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
                    Beautiful datepicker styling using Flowbite design system with Tailwind CSS
                </p>
                
                <!-- Dark Mode Toggle -->
                <button id="theme-toggle" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                    <svg id="theme-toggle-dark-icon" class="hidden w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                    <svg id="theme-toggle-light-icon" class="hidden w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 17.77L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z"></path>
                    </svg>
                    Toggle Dark Mode
                </button>
            </div>

            <!-- Demo Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                
                <!-- Basic Datepicker -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Basic Datepicker
                    </h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Simple date selection with Flowbite styling
                    </p>
                    
                    <div class="flowbite-datepicker-container">
                        <div class="flowbite-datepicker-icon">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input type="text" id="basic-datepicker" class="flowbite-datepicker-input" placeholder="Select date">
                    </div>
                </div>

                <!-- Enhanced Header Datepicker -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Enhanced Header
                    </h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Clear month and year display with dropdowns
                    </p>

                    <div class="flowbite-datepicker-container">
                        <div class="flowbite-datepicker-icon">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input type="text" id="enhanced-header-datepicker" class="flowbite-datepicker-input" placeholder="Select date">
                    </div>
                </div>

                <!-- Datepicker with Custom Header -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Custom Header Style
                    </h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Beautiful gradient header with month/year
                    </p>

                    <div class="flowbite-datepicker-container">
                        <div class="flowbite-datepicker-icon">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input type="text" id="custom-header-datepicker" class="flowbite-datepicker-input" placeholder="Select date">
                    </div>
                </div>

                <!-- Date Range Picker -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Date Range Picker
                    </h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Select a range of dates
                    </p>
                    
                    <div class="flex items-center space-x-4">
                        <div class="flowbite-datepicker-container flex-1">
                            <div class="flowbite-datepicker-icon">
                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                                </svg>
                            </div>
                            <input type="text" id="range-start" class="flowbite-datepicker-input" placeholder="Start date">
                        </div>
                        
                        <span class="text-gray-500 dark:text-gray-400">to</span>
                        
                        <div class="flowbite-datepicker-container flex-1">
                            <div class="flowbite-datepicker-icon">
                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                                </svg>
                            </div>
                            <input type="text" id="range-end" class="flowbite-datepicker-input" placeholder="End date">
                        </div>
                    </div>
                </div>

                <!-- Inline Datepicker -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Inline Datepicker
                    </h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Always visible calendar
                    </p>
                    
                    <div id="inline-datepicker"></div>
                </div>
            </div>

            <!-- Features Section -->
            <div class="mt-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    Features
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Tailwind CSS Classes</h3>
                            <p class="text-gray-600 dark:text-gray-300">Built using Flowbite's Tailwind utility classes</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Dark Mode Support</h3>
                            <p class="text-gray-600 dark:text-gray-300">Automatic dark mode with proper contrast</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Responsive Design</h3>
                            <p class="text-gray-600 dark:text-gray-300">Works perfectly on all device sizes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flatpickr JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <script>
        // Dark mode toggle functionality
        const themeToggleBtn = document.getElementById('theme-toggle');
        const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
        const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');

        // Check for saved theme preference or default to 'light'
        const currentTheme = localStorage.getItem('theme') || 'light';
        
        if (currentTheme === 'dark') {
            document.documentElement.classList.add('dark');
            themeToggleLightIcon.classList.remove('hidden');
        } else {
            themeToggleDarkIcon.classList.remove('hidden');
        }

        themeToggleBtn.addEventListener('click', function() {
            // Toggle icons
            themeToggleDarkIcon.classList.toggle('hidden');
            themeToggleLightIcon.classList.toggle('hidden');

            // Toggle dark mode
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        });

        // Initialize Flatpickr instances
        
        // Basic datepicker
        flatpickr("#basic-datepicker", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
            }
        });

        // Enhanced Header Datepicker
        flatpickr("#enhanced-header-datepicker", {
            dateFormat: "Y-m-d",
            enableTime: false,
            monthSelectorType: 'dropdown',
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');

                // Enhance the header display
                const currentMonth = instance.currentMonth;
                const currentYear = instance.currentYear;

                // Add month/year separator
                const monthElement = instance.monthNav;
                if (monthElement) {
                    const separator = document.createElement('span');
                    separator.className = 'month-year-separator';
                    separator.textContent = '•';
                    monthElement.appendChild(separator);
                }
            },
            onMonthChange: function(selectedDates, dateStr, instance) {
                // Update header when month changes
                console.log(`Month changed to: ${instance.currentMonth + 1}/${instance.currentYear}`);
            }
        });

        // Custom Header Datepicker
        flatpickr("#custom-header-datepicker", {
            dateFormat: "Y-m-d",
            monthSelectorType: 'dropdown',
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');

                // Create custom header
                const customHeader = document.createElement('div');
                customHeader.className = 'flatpickr-custom-header';

                const monthNames = [
                    'January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'
                ];

                const updateHeader = () => {
                    const monthName = monthNames[instance.currentMonth];
                    const year = instance.currentYear;
                    customHeader.textContent = `${monthName} ${year}`;
                };

                updateHeader();

                // Insert custom header at the beginning
                instance.calendarContainer.insertBefore(customHeader, instance.calendarContainer.firstChild);

                // Update header when month/year changes
                instance.config.onMonthChange.push(updateHeader);
                instance.config.onYearChange.push(updateHeader);
            }
        });

        // Date range picker
        const rangeStart = flatpickr("#range-start", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
            },
            onChange: function(selectedDates, dateStr, instance) {
                rangeEnd.set('minDate', dateStr);
            }
        });

        const rangeEnd = flatpickr("#range-end", {
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
            },
            onChange: function(selectedDates, dateStr, instance) {
                rangeStart.set('maxDate', dateStr);
            }
        });

        // Inline datepicker
        flatpickr("#inline-datepicker", {
            inline: true,
            dateFormat: "Y-m-d",
            onReady: function(selectedDates, dateStr, instance) {
                instance.calendarContainer.classList.add('flowbite-theme');
            }
        });
    </script>
</body>
</html>
