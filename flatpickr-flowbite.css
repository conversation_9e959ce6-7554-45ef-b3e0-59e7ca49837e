/* Flowbite Theme for Flatpickr - Using Tailwind CSS Classes */

:root {
  /* Flowbite Color Variables */
  --flowbite-primary: #3b82f6;
  --flowbite-primary-hover: #2563eb;
  --flowbite-primary-focus: #1d4ed8;
  --flowbite-gray-50: #f9fafb;
  --flowbite-gray-100: #f3f4f6;
  --flowbite-gray-200: #e5e7eb;
  --flowbite-gray-300: #d1d5db;
  --flowbite-gray-400: #9ca3af;
  --flowbite-gray-500: #6b7280;
  --flowbite-gray-600: #4b5563;
  --flowbite-gray-700: #374151;
  --flowbite-gray-800: #1f2937;
  --flowbite-gray-900: #111827;
  
  /* Dark mode colors */
  --flowbite-dark-bg: #1f2937;
  --flowbite-dark-border: #374151;
  --flowbite-dark-text: #f9fafb;
  --flowbite-dark-text-secondary: #d1d5db;
  
  /* Shadows */
  --flowbite-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --flowbite-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border radius */
  --flowbite-radius: 0.5rem;
  --flowbite-radius-lg: 0.75rem;
}

/* Main Calendar Container - Equivalent to Tailwind: bg-white border border-gray-200 rounded-lg shadow-lg */
.flatpickr-calendar.flowbite-theme {
  background-color: white !important;
  border: 1px solid var(--flowbite-gray-200) !important;
  border-radius: var(--flowbite-radius-lg) !important;
  box-shadow: var(--flowbite-shadow-lg) !important;
  padding: 1.5rem !important;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  color: var(--flowbite-gray-900) !important;
  width: 20rem !important;
  animation: flowbiteSlideIn 0.15s ease-out !important;
}

/* Dark mode support */
.dark .flatpickr-calendar.flowbite-theme {
  background-color: var(--flowbite-dark-bg) !important;
  border-color: var(--flowbite-dark-border) !important;
  color: var(--flowbite-dark-text) !important;
}

/* Calendar Header - Equivalent to Tailwind: pb-4 mb-4 */
.flatpickr-calendar.flowbite-theme .flatpickr-months {
  padding-bottom: 1rem !important;
  margin-bottom: 1rem !important;
  border-bottom: none !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-month {
  background: transparent !important;
  color: var(--flowbite-gray-900) !important;
  height: auto !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-month {
  color: var(--flowbite-dark-text) !important;
}

/* Current Month Display - Enhanced Header with Month/Year */
.flatpickr-calendar.flowbite-theme .flatpickr-current-month {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: var(--flowbite-gray-900) !important;
  padding: 0.5rem 0 !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  min-height: 2.5rem !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-current-month {
  color: var(--flowbite-dark-text) !important;
}

/* Month Dropdown Styling */
.flatpickr-calendar.flowbite-theme .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: var(--flowbite-gray-50) !important;
  border: 1px solid var(--flowbite-gray-200) !important;
  border-radius: 0.5rem !important;
  color: var(--flowbite-gray-900) !important;
  font-weight: 600 !important;
  font-size: 1.125rem !important;
  padding: 0.375rem 0.75rem !important;
  margin: 0 !important;
  cursor: pointer !important;
  transition: all 0.15s ease-in-out !important;
  min-width: 7rem !important;
  text-align: center !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background: var(--flowbite-gray-100) !important;
  border-color: var(--flowbite-gray-300) !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-current-month .flatpickr-monthDropdown-months:focus {
  outline: none !important;
  border-color: var(--flowbite-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: var(--flowbite-dark-bg) !important;
  border-color: var(--flowbite-dark-border) !important;
  color: var(--flowbite-dark-text) !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background: var(--flowbite-gray-700) !important;
  border-color: var(--flowbite-gray-600) !important;
}

/* Year Input Wrapper */
.flatpickr-calendar.flowbite-theme .numInputWrapper {
  background: var(--flowbite-gray-50) !important;
  border: 1px solid var(--flowbite-gray-200) !important;
  border-radius: 0.5rem !important;
  padding: 0 !important;
  width: auto !important;
  transition: all 0.15s ease-in-out !important;
}

.flatpickr-calendar.flowbite-theme .numInputWrapper:hover {
  background: var(--flowbite-gray-100) !important;
  border-color: var(--flowbite-gray-300) !important;
}

.flatpickr-calendar.flowbite-theme .numInputWrapper:focus-within {
  border-color: var(--flowbite-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.dark .flatpickr-calendar.flowbite-theme .numInputWrapper {
  background: var(--flowbite-dark-bg) !important;
  border-color: var(--flowbite-dark-border) !important;
}

.dark .flatpickr-calendar.flowbite-theme .numInputWrapper:hover {
  background: var(--flowbite-gray-700) !important;
  border-color: var(--flowbite-gray-600) !important;
}

/* Year Input Field */
.flatpickr-calendar.flowbite-theme .numInputWrapper input {
  color: var(--flowbite-gray-900) !important;
  font-weight: 600 !important;
  font-size: 1.125rem !important;
  background: transparent !important;
  border: none !important;
  text-align: center !important;
  width: 4.5rem !important;
  padding: 0.375rem 0.75rem !important;
  outline: none !important;
}

.dark .flatpickr-calendar.flowbite-theme .numInputWrapper input {
  color: var(--flowbite-dark-text) !important;
}

/* Custom Header Title (Alternative approach) */
.flatpickr-calendar.flowbite-theme .flatpickr-custom-header {
  background: linear-gradient(135deg, var(--flowbite-primary) 0%, var(--flowbite-primary-hover) 100%) !important;
  color: white !important;
  padding: 1rem !important;
  margin: -1.5rem -1.5rem 1rem -1.5rem !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
  text-align: center !important;
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  letter-spacing: 0.025em !important;
}

/* Month/Year separator */
.flatpickr-calendar.flowbite-theme .flatpickr-current-month .month-year-separator {
  color: var(--flowbite-gray-400) !important;
  font-weight: 400 !important;
  margin: 0 0.25rem !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-current-month .month-year-separator {
  color: var(--flowbite-gray-500) !important;
}

/* Navigation Arrows - Equivalent to Tailwind: w-8 h-8 rounded-full hover:bg-gray-100 */
.flatpickr-calendar.flowbite-theme .flatpickr-prev-month,
.flatpickr-calendar.flowbite-theme .flatpickr-next-month {
  width: 2rem !important;
  height: 2rem !important;
  border-radius: 9999px !important;
  background: transparent !important;
  border: none !important;
  transition: all 0.15s ease-in-out !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--flowbite-gray-500) !important;
  cursor: pointer !important;
  top: 1rem !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-prev-month:hover,
.flatpickr-calendar.flowbite-theme .flatpickr-next-month:hover {
  background-color: var(--flowbite-gray-100) !important;
  color: var(--flowbite-gray-700) !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-prev-month:hover,
.dark .flatpickr-calendar.flowbite-theme .flatpickr-next-month:hover {
  background-color: var(--flowbite-gray-700) !important;
  color: var(--flowbite-dark-text) !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-prev-month svg,
.flatpickr-calendar.flowbite-theme .flatpickr-next-month svg {
  width: 1rem !important;
  height: 1rem !important;
  fill: currentColor !important;
}

/* Weekdays - Equivalent to Tailwind: text-xs font-medium text-gray-500 uppercase */
.flatpickr-calendar.flowbite-theme .flatpickr-weekdays {
  background: transparent !important;
  margin: 0.75rem 0 0.5rem 0 !important;
  padding: 0 !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-weekday {
  background: transparent !important;
  color: var(--flowbite-gray-500) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  padding: 0.5rem 0 !important;
  text-align: center !important;
  border-radius: 0 !important;
  margin: 0 !important;
  width: 2.5rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-weekday {
  color: var(--flowbite-dark-text-secondary) !important;
}

/* Calendar Days Container */
.flatpickr-calendar.flowbite-theme .flatpickr-days {
  width: 100% !important;
  padding: 0 !important;
}

/* Calendar Days - Equivalent to Tailwind: w-10 h-10 rounded-lg hover:bg-gray-100 focus:bg-blue-500 */
.flatpickr-calendar.flowbite-theme .flatpickr-day {
  background: transparent !important;
  border: none !important;
  border-radius: var(--flowbite-radius) !important;
  color: var(--flowbite-gray-900) !important;
  font-size: 0.875rem !important;
  font-weight: 400 !important;
  height: 2.5rem !important;
  width: 2.5rem !important;
  line-height: 2.5rem !important;
  margin: 0.125rem !important;
  transition: all 0.15s ease-in-out !important;
  cursor: pointer !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-day {
  color: var(--flowbite-dark-text) !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-day:hover {
  background-color: var(--flowbite-gray-100) !important;
  color: var(--flowbite-gray-900) !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-day:hover {
  background-color: var(--flowbite-gray-700) !important;
  color: var(--flowbite-dark-text) !important;
}

/* Selected Day - Equivalent to Tailwind: bg-blue-500 text-white */
.flatpickr-calendar.flowbite-theme .flatpickr-day.selected {
  background-color: var(--flowbite-primary) !important;
  color: white !important;
  font-weight: 500 !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-day.selected:hover {
  background-color: var(--flowbite-primary-hover) !important;
}

/* Today - Equivalent to Tailwind: bg-blue-50 text-blue-600 */
.flatpickr-calendar.flowbite-theme .flatpickr-day.today {
  background-color: #eff6ff !important;
  color: var(--flowbite-primary) !important;
  font-weight: 500 !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-day.today {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #60a5fa !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-day.today:hover {
  background-color: #dbeafe !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-day.today:hover {
  background-color: rgba(59, 130, 246, 0.2) !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-day.today.selected {
  background-color: var(--flowbite-primary) !important;
  color: white !important;
}

/* Previous/Next Month Days - Equivalent to Tailwind: text-gray-400 */
.flatpickr-calendar.flowbite-theme .flatpickr-day.prevMonthDay,
.flatpickr-calendar.flowbite-theme .flatpickr-day.nextMonthDay {
  color: var(--flowbite-gray-400) !important;
  opacity: 0.6 !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-day.prevMonthDay,
.dark .flatpickr-calendar.flowbite-theme .flatpickr-day.nextMonthDay {
  color: var(--flowbite-gray-600) !important;
}

/* Disabled Days - Equivalent to Tailwind: text-gray-300 cursor-not-allowed */
.flatpickr-calendar.flowbite-theme .flatpickr-day.disabled {
  color: var(--flowbite-gray-300) !important;
  cursor: not-allowed !important;
  background: transparent !important;
  opacity: 0.4 !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-day.disabled:hover {
  background: transparent !important;
  color: var(--flowbite-gray-300) !important;
  cursor: not-allowed !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-day.disabled {
  color: var(--flowbite-gray-600) !important;
}

/* Animation */
.flatpickr-calendar.flowbite-theme.open {
  animation: flowbiteSlideIn 0.15s ease-out;
}

@keyframes flowbiteSlideIn {
  from {
    opacity: 0;
    transform: translateY(-0.5rem) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Input Field Styling - Equivalent to Tailwind: bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 */
.flowbite-datepicker-input {
  width: 100%;
  padding: 0.625rem 0.75rem;
  padding-left: 2.5rem; /* Space for icon */
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--flowbite-gray-900);
  background-color: var(--flowbite-gray-50);
  border: 1px solid var(--flowbite-gray-300);
  border-radius: var(--flowbite-radius-lg);
  transition: all 0.15s ease-in-out;
  box-sizing: border-box;
  font-weight: 400;
}

.dark .flowbite-datepicker-input {
  background-color: var(--flowbite-dark-bg);
  border-color: var(--flowbite-dark-border);
  color: var(--flowbite-dark-text);
}

.flowbite-datepicker-input:hover {
  border-color: var(--flowbite-gray-400);
}

.dark .flowbite-datepicker-input:hover {
  border-color: var(--flowbite-gray-600);
}

.flowbite-datepicker-input:focus {
  outline: none;
  border-color: var(--flowbite-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.flowbite-datepicker-input::placeholder {
  color: var(--flowbite-gray-400);
  font-weight: 400;
}

.dark .flowbite-datepicker-input::placeholder {
  color: var(--flowbite-gray-500);
}

/* Input Container with Icon - Equivalent to Tailwind: relative */
.flowbite-datepicker-container {
  position: relative;
  max-width: 20rem;
}

/* Calendar Icon - Equivalent to Tailwind: absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none */
.flowbite-datepicker-icon {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  padding-left: 0.75rem;
  pointer-events: none;
}

.flowbite-datepicker-icon svg {
  width: 1rem;
  height: 1rem;
  color: var(--flowbite-gray-500);
}

.dark .flowbite-datepicker-icon svg {
  color: var(--flowbite-gray-400);
}

/* Action Buttons (Today/Clear) - Equivalent to Tailwind: flex justify-between pt-4 border-t border-gray-200 */
.flatpickr-calendar.flowbite-theme .flatpickr-time {
  border-top: 1px solid var(--flowbite-gray-200);
  padding-top: 1rem;
  margin-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-time {
  border-color: var(--flowbite-dark-border);
}

/* Today Button - Equivalent to Tailwind: text-blue-600 hover:text-blue-800 font-medium text-sm */
.flatpickr-calendar.flowbite-theme .flatpickr-today-button {
  background: none;
  border: none;
  color: var(--flowbite-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: var(--flowbite-radius);
  transition: all 0.15s ease-in-out;
}

.flatpickr-calendar.flowbite-theme .flatpickr-today-button:hover {
  color: var(--flowbite-primary-hover);
  background-color: #eff6ff;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-today-button:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

/* Clear Button - Equivalent to Tailwind: text-gray-600 hover:text-gray-800 font-medium text-sm */
.flatpickr-calendar.flowbite-theme .flatpickr-clear-button {
  background: none;
  border: none;
  color: var(--flowbite-gray-600);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: var(--flowbite-radius);
  transition: all 0.15s ease-in-out;
}

.flatpickr-calendar.flowbite-theme .flatpickr-clear-button:hover {
  color: var(--flowbite-gray-800);
  background-color: var(--flowbite-gray-100);
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-clear-button {
  color: var(--flowbite-dark-text-secondary);
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-clear-button:hover {
  color: var(--flowbite-dark-text);
  background-color: var(--flowbite-gray-700);
}

/* Range Picker Support - Equivalent to Tailwind: bg-blue-100 */
.flatpickr-calendar.flowbite-theme .flatpickr-day.inRange {
  background-color: #dbeafe !important;
  color: var(--flowbite-primary) !important;
  border-radius: 0 !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-day.inRange {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #60a5fa !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-day.startRange {
  border-radius: var(--flowbite-radius) 0 0 var(--flowbite-radius) !important;
  background-color: var(--flowbite-primary) !important;
  color: white !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-day.endRange {
  border-radius: 0 var(--flowbite-radius) var(--flowbite-radius) 0 !important;
  background-color: var(--flowbite-primary) !important;
  color: white !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-day.startRange.endRange {
  border-radius: var(--flowbite-radius) !important;
}

/* Additional Header Styles */

/* Minimal Text Header */
.flatpickr-calendar.flowbite-theme .flatpickr-minimal-header {
  text-align: center !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  color: var(--flowbite-gray-700) !important;
  padding: 0.75rem 0 !important;
  margin-bottom: 0.5rem !important;
  border-bottom: 1px solid var(--flowbite-gray-200) !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-minimal-header {
  color: var(--flowbite-dark-text) !important;
  border-color: var(--flowbite-dark-border) !important;
}

/* Badge Style Header */
.flatpickr-calendar.flowbite-theme .flatpickr-badge-header {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.75rem 0 !important;
  margin-bottom: 0.5rem !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-month-badge {
  background: var(--flowbite-primary) !important;
  color: white !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 9999px !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-year-badge {
  background: var(--flowbite-gray-100) !important;
  color: var(--flowbite-gray-700) !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 9999px !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-year-badge {
  background: var(--flowbite-gray-700) !important;
  color: var(--flowbite-dark-text) !important;
}

/* Header with Icons */
.flatpickr-calendar.flowbite-theme .flatpickr-icon-header {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.75rem 0 !important;
  margin-bottom: 0.5rem !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  color: var(--flowbite-gray-900) !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-icon-header {
  color: var(--flowbite-dark-text) !important;
}

.flatpickr-calendar.flowbite-theme .flatpickr-icon-header svg {
  width: 1.25rem !important;
  height: 1.25rem !important;
  color: var(--flowbite-primary) !important;
}

/* Compact Header */
.flatpickr-calendar.flowbite-theme .flatpickr-compact-header {
  background: var(--flowbite-gray-50) !important;
  border: 1px solid var(--flowbite-gray-200) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  margin: 0 0 1rem 0 !important;
  text-align: center !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: var(--flowbite-gray-700) !important;
}

.dark .flatpickr-calendar.flowbite-theme .flatpickr-compact-header {
  background: var(--flowbite-gray-700) !important;
  border-color: var(--flowbite-dark-border) !important;
  color: var(--flowbite-dark-text) !important;
}

/* Header Animation */
.flatpickr-calendar.flowbite-theme .flatpickr-custom-header,
.flatpickr-calendar.flowbite-theme .flatpickr-minimal-header,
.flatpickr-calendar.flowbite-theme .flatpickr-badge-header,
.flatpickr-calendar.flowbite-theme .flatpickr-icon-header,
.flatpickr-calendar.flowbite-theme .flatpickr-compact-header {
  animation: headerFadeIn 0.3s ease-out !important;
}

@keyframes headerFadeIn {
  from {
    opacity: 0;
    transform: translateY(-0.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hide default header when using custom headers */
.flatpickr-calendar.flowbite-theme.custom-header .flatpickr-months {
  display: none !important;
}

/* Responsive Design */
@media (max-width: 640px) {
  .flatpickr-calendar.flowbite-theme {
    width: 18rem !important;
    padding: 1rem !important;
  }

  .flowbite-datepicker-container {
    max-width: 100%;
  }

  .flatpickr-calendar.flowbite-theme .flatpickr-custom-header {
    font-size: 1.125rem !important;
    padding: 0.75rem !important;
  }

  .flatpickr-calendar.flowbite-theme .flatpickr-badge-header {
    flex-direction: column !important;
    gap: 0.25rem !important;
  }
}
