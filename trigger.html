<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <x-test>
        Click
    </x-test>
    <script>
        class Test extends HTMLElement {
            constructor() {
                super();
                this._count = 0; // Use a backing variable for count
                this.addEventListener('click', this.countChange.bind(this));
            }

            get count() {
                return this._count;
            }

            set count(value) {
                this._count = value;
                this.triggerChange();
            }

            triggerChange() {
                console.log('triggerChange', this._count);
            }

            countChange() {
                this.count++; // Use the setter to trigger change
            }
        }

        customElements.define('x-test', Test);
    </script>
</body>
</html>
