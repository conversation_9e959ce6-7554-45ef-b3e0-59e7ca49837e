<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DatePicker Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="datepicker.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white p-6 rounded-lg shadow-lg">
        <h1 class="text-2xl font-bold mb-6">DatePicker Test</h1>
        
        <!-- Simple Test -->
        <div class="mb-6">
            <div class="datepicker" data-variant="bordered" data-size="md">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Date</label>
                <div class="datepicker-input-wrapper">
                    <input type="text" class="datepicker-input" placeholder="Click to select date" readonly>
                    <button class="datepicker-trigger" type="button">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- With Time Slots -->
        <div class="mb-6">
            <div class="datepicker" data-variant="bordered" data-size="md" data-show-time="true" data-time-slots="true">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Date & Time Slot</label>
                <div class="datepicker-input-wrapper">
                    <input type="text" class="datepicker-input" placeholder="Click to select date and time" readonly>
                    <button class="datepicker-trigger" type="button">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="font-medium text-gray-700 mb-2">Test Results:</h3>
            <div id="test-results" class="text-sm text-gray-600">
                Click on the datepickers above to test functionality
            </div>
        </div>

        <!-- Test Buttons -->
        <div class="mt-6 space-y-2">
            <button onclick="testProgrammaticSet()" class="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                Test Programmatic Set Date
            </button>
            <button onclick="testValidation()" class="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                Test Validation
            </button>
            <button onclick="clearAll()" class="w-full bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                Clear All
            </button>
        </div>
    </div>

    <!-- Calendar Popup Template (Hidden) -->
    <div id="calendar-popup" class="datepicker-popup hidden">
        <div class="datepicker-popup-content">
            <!-- Calendar Header -->
            <div class="calendar-header">
                <button class="calendar-nav-btn" id="prev-month" type="button">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <div class="calendar-title">
                    <span id="current-month-year">January 2024</span>
                </div>
                <button class="calendar-nav-btn" id="next-month" type="button">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>

            <!-- Calendar Grid -->
            <div class="calendar-grid">
                <div class="calendar-weekdays">
                    <div class="calendar-weekday">Su</div>
                    <div class="calendar-weekday">Mo</div>
                    <div class="calendar-weekday">Tu</div>
                    <div class="calendar-weekday">We</div>
                    <div class="calendar-weekday">Th</div>
                    <div class="calendar-weekday">Fr</div>
                    <div class="calendar-weekday">Sa</div>
                </div>
                <div class="calendar-days" id="calendar-days">
                    <!-- Days will be generated by JavaScript -->
                </div>
            </div>

            <!-- Time Picker -->
            <div class="time-picker hidden" id="time-picker">
                <div class="time-picker-header">
                    <h4 class="text-sm font-medium text-gray-700">Select Time</h4>
                </div>
                <div class="time-inputs">
                    <div class="time-input-group">
                        <label class="time-label">Hour</label>
                        <select class="time-select" id="hour-select"></select>
                    </div>
                    <div class="time-input-group">
                        <label class="time-label">Minute</label>
                        <select class="time-select" id="minute-select"></select>
                    </div>
                    <div class="time-input-group">
                        <label class="time-label">Period</label>
                        <select class="time-select" id="period-select">
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Time Slots -->
            <div class="time-slots hidden" id="time-slots">
                <div class="time-slots-header">
                    <h4 class="text-sm font-medium text-gray-700">Available Time Slots</h4>
                </div>
                <div class="time-slots-grid" id="time-slots-grid">
                    <!-- Time slots will be generated by JavaScript -->
                </div>
            </div>

            <!-- Footer Actions -->
            <div class="calendar-footer">
                <button class="calendar-btn calendar-btn-secondary" id="calendar-cancel" type="button">Cancel</button>
                <button class="calendar-btn calendar-btn-primary" id="calendar-confirm" type="button">Confirm</button>
            </div>
        </div>
    </div>

    <script src="datepicker.js"></script>
    <script>
        let datepickerInstances = [];

        // Store references to datepicker instances
        document.addEventListener('DOMContentLoaded', function() {
            const datepickers = document.querySelectorAll('.datepicker');
            datepickers.forEach((element, index) => {
                const instance = new DatePicker(element);
                datepickerInstances[index] = instance;
                
                // Listen for date changes
                element.addEventListener('datechange', function(e) {
                    updateTestResults(`Datepicker ${index + 1}: ${e.detail.formatted}`);
                });
            });
        });

        function updateTestResults(message) {
            const results = document.getElementById('test-results');
            results.innerHTML += '<br>' + message;
        }

        function testProgrammaticSet() {
            if (datepickerInstances[0]) {
                const today = new Date();
                datepickerInstances[0].setValue(today, '2:30 PM');
                updateTestResults('Programmatically set first datepicker to today at 2:30 PM');
            }
        }

        function testValidation() {
            datepickerInstances.forEach((instance, index) => {
                if (instance) {
                    const validation = instance.validate();
                    updateTestResults(`Datepicker ${index + 1} validation: ${validation.valid ? 'Valid' : validation.message}`);
                }
            });
        }

        function clearAll() {
            datepickerInstances.forEach((instance, index) => {
                if (instance) {
                    instance.clear();
                }
            });
            document.getElementById('test-results').innerHTML = 'All datepickers cleared';
        }
    </script>
</body>
</html>
