<html>
<head>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"></link>
</head>
<body class="bg-white flex justify-center items-center min-h-screen">
    <div class="w-full max-w-lg p-8">
        <h1 class="text-center text-2xl font-bold text-teal-500 mb-8">新規会員登録</h1>
        <form>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="name">
                    お名前 <span class="text-red-500">(必須)</span>
                </label>
                <div class="flex space-x-4">
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="last-name" type="text" placeholder="姓">
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="first-name" type="text" placeholder="名">
                </div>
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="prefecture">
                    都道府県 <span class="text-red-500">(必須)</span>
                </label>
                <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="prefecture">
                    <option>都道府県を選択</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
                    メールアドレス <span class="text-red-500">(必須)</span>
                </label>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="email" type="email" value="<EMAIL>">
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="password">
                    パスワード <span class="text-red-500">(必須)</span>
                </label>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="password" type="password" value="********">
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="purpose">
                    ご利用目的 <span class="text-red-500">(必須)</span>
                </label>
                <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="purpose">
                    <option>ご利用目的を選択</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="date">
                    ご利用予定日/挙式予定日 <span class="text-red-500">(必須)</span>
                </label>
                <div class="flex items-center mb-2">
                    <input type="radio" id="calendar" name="date-option" class="mr-2">
                    <label for="calendar" class="text-gray-700 text-sm">カレンダーから選ぶ</label>
                </div>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-2" id="date" type="text" placeholder="dd/mm/yyyy">
                <div class="flex items-center mb-2">
                    <input type="radio" id="time" name="date-option" class="mr-2">
                    <label for="time" class="text-gray-700 text-sm">時期から選ぶ</label>
                </div>
                <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-2" id="time-select">
                    <option>ご利用予定日を選択</option>
                </select>
                <div class="flex items-center mb-2">
                    <input type="radio" id="undecided" name="date-option" class="mr-2">
                    <label for="undecided" class="text-gray-700 text-sm">まだ決まっていない</label>
                </div>
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="venue">
                    利用予定会場 <span class="text-red-500">(必須)</span>
                </label>
                <div class="flex items-center mb-2">
                    <input type="radio" id="free-input" name="venue-option" class="mr-2">
                    <label for="free-input" class="text-gray-700 text-sm">自由入力</label>
                </div>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="venue" type="text" placeholder="自由入力">
            </div>
        </form>
    </div>
</body>
</html>