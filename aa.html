<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Matches Example</title>
    <style>
        /* Basic CSS styles */
        body {
            font-family: sans-serif;
            margin: 0;
            background-color: #f0f0f0; /* Light grey background */
            color: #333; /* Default text color */
        }

        .container {
            width: 95%; /* Slightly wider container */
            max-width: 1200px; /* Max width for larger screens */
            margin: 0 auto; /* Center the container */
            padding: 0 10px; /* Add some padding on the sides */
        }

        header {
            background-color: #ffffff; /* White background */
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow */
        }

        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff; /* Example logo color */
        }

        nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
        }

        nav ul li {
            margin-left: 20px;
        }

        nav a {
            text-decoration: none;
            color: #333;
            font-weight: bold;
            transition: color 0.3s ease;
        }

        nav a:hover {
            color: #007bff;
        }

        .auth-links a {
            text-decoration: none;
            color: #007bff;
            margin-left: 10px;
        }

        /* Main content layout */
        .content-layout {
            display: grid;
            grid-template-columns: 1fr; /* Stack columns by default */
            gap: 20px;
            margin-top: 20px;
        }

        @media (min-width: 768px) {
            .content-layout {
                grid-template-columns: 2fr 1fr; /* Two columns on wider screens */
            }
        }


        .live-matches, .sidebar {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .live-matches h2 {
            margin-top: 0;
            color: #333;
        }

        .match-item {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }

        .match-item h3 {
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 18px;
        }

        .match-item p {
            margin: 5px 0;
            font-size: 14px;
            color: #555;
        }

        .sidebar h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>

    <header>
        <div class="container">
            <div class="logo">OK9VIP</div>
            <nav>
                <ul>
                    <li><a href="#">Trang Chủ</a></li>
                    <li><a href="#">Sảnh Cược</a></li>
                    <li><a href="#">Xổ Số</a></li>
                    <li><a href="#">Quản Quán</a></li>
                    <li><a href="#">Kết Quả</a></li>
                    <li><a href="#">Game+</a></li>
                    <li><a href="#">Lịch Sử</a></li>
                    <li><a href="#">Khuyến Mãi</a></li>
                    <li><a href="#">LIVE</a></li>
                    <li><a href="#">Tải Trợ</a></li>
                    <li><a href="#">Đại Lý</a></li>
                    <li><a href="#">Tài Khoản</a></li>
                </ul>
            </nav>
            <div class="auth-links">
                <a href="#">Đăng Nhập</a> | <a href="#">Đăng Ký</a>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <section class="info-bar" style="background-color: #ffffe0; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
                Thông tin chạy ngang đây... (use JavaScript for scrolling effect)
            </section>

            <div class="content-layout">
                <section class="live-matches">
                    <h2>Trong Trận</h2>
                    <div id="live-matches-list">
                        </div>
                </section>

                <aside class="sidebar">
                    <h3>Sidebar Content</h3>
                    <p>This is where specific match details or promotions could go.</p>
                    <div style="background-color: #e9f7ef; padding: 15px; border-radius: 5px; margin-top: 15px;">
                        <h4>Special Offer!</h4>
                        <p>Join now and get a bonus.</p>
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <footer style="text-align: center; padding: 20px 0; margin-top: 30px; background-color: #333; color: #fff;">
        <div class="container">
            &copy; 2023 Your Website Name. All rights reserved.
        </div>
    </footer>

    <script>
        // Basic JavaScript to add a dummy match item
        document.addEventListener('DOMContentLoaded', function() {
            const liveMatchesList = document.getElementById('live-matches-list');

            // Dummy data - in a real app, fetch this from an API
            const dummyMatch = {
                team1: 'Montego Bay United FC',
                team2: 'Tivoli Gardens FC',
                score: '1 - 0',
                time: 'HT 91:39'
            };

            // Create and add the match item to the list
            const matchElement = document.createElement('div');
            matchElement.classList.add('match-item');
            matchElement.innerHTML = `
                <h3>${dummyMatch.team1} vs ${dummyMatch.team2}</h3>
                <p>Score: ${dummyMatch.score}</p>
                <p>Time: ${dummyMatch.time}</p>
                `;

            liveMatchesList.appendChild(matchElement);

            // You would add more JavaScript here for dynamic features:
            // - Fetching real-time data
            // - Handling clicks on filters
            // - Implementing a banner carousel
            // - Making elements interactive
        });
    </script>

</body>
</html>