const data = {
    "data": {
        "products": {
            "edges": [
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU1NTQ0NjAwLCJsYXN0X3ZhbHVlIjo5NTkyNjU1NTQ0NjAwfQ==",
                    "node": {
                        "title": "辻利兵衛本店　賽の茶4個 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885051672"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU1NjQyOTA0LCJsYXN0X3ZhbHVlIjo5NTkyNjU1NjQyOTA0fQ==",
                    "node": {
                        "title": "辻利兵衛本店　賽の茶6個 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885215512"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU1Njc1NjcyLCJsYXN0X3ZhbHVlIjo5NTkyNjU1Njc1NjcyfQ==",
                    "node": {
                        "title": "ア・ラ・カンパーニュ　焼菓子詰合せ　8個 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885248280"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU1ODA2NzQ0LCJsYXN0X3ZhbHVlIjo5NTkyNjU1ODA2NzQ0fQ==",
                    "node": {
                        "title": "花園万頭　花園最中　4個　【結婚式　ギフト　食品　引き菓子　和菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885379352"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU1ODM5NTEyLCJsYXN0X3ZhbHVlIjo5NTkyNjU1ODM5NTEyfQ==",
                    "node": {
                        "title": "ア・ラ・カンパーニュ　イチゴのパウンドケーキ(ケークフレーズ) 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885412120"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU1ODcyMjgwLCJsYXN0X3ZhbHVlIjo5NTkyNjU1ODcyMjgwfQ==",
                    "node": {
                        "title": "ガトー・ド・ボワイヤージュ　横浜馬車道ミルフイユ&スフレ　7個 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885444888"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU1OTA1MDQ4LCJsYXN0X3ZhbHVlIjo5NTkyNjU1OTA1MDQ4fQ==",
                    "node": {
                        "title": "ホシフルーツ ナッツとドライフルーツの贅沢ブラウニー6個 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885477656"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU1OTM3ODE2LCJsYXN0X3ZhbHVlIjo5NTkyNjU1OTM3ODE2fQ==",
                    "node": {
                        "title": "なだ万監修 輪の絆和三盆バウムクーヘン 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885510424"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2MDAzMzUyLCJsYXN0X3ZhbHVlIjo5NTkyNjU2MDAzMzUyfQ==",
                    "node": {
                        "title": "ホシフルーツ ハードバウム 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885575960"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2MDM2MTIwLCJsYXN0X3ZhbHVlIjo5NTkyNjU2MDM2MTIwfQ==",
                    "node": {
                        "title": "ホシフルーツ　フレンチカップケーキ4個 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885608728"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2MDY4ODg4LCJsYXN0X3ZhbHVlIjo5NTkyNjU2MDY4ODg4fQ==",
                    "node": {
                        "title": "ホシフルーツ 果実のミニョン・ド・クグロフ6個 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885641496"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2MTAxNjU2LCJsYXN0X3ZhbHVlIjo5NTkyNjU2MTAxNjU2fQ==",
                    "node": {
                        "title": "ホシフルーツ ナッツとドライフルーツの贅沢ブラウニー9個 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885674264"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2MTY3MTkyLCJsYXN0X3ZhbHVlIjo5NTkyNjU2MTY3MTkyfQ==",
                    "node": {
                        "title": "ホシフルーツ 大人のチーズケーキ 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885739800"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2MjMyNzI4LCJsYXN0X3ZhbHVlIjo5NTkyNjU2MjMyNzI4fQ==",
                    "node": {
                        "title": "六本木アマンド ROPPONGI CAKE BATON<六本木ケイクバトン>4本 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885805336"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2MzYzODAwLCJsYXN0X3ZhbHVlIjo5NTkyNjU2MzYzODAwfQ==",
                    "node": {
                        "title": "なだ万監修 輪の絆 小豆芋バウムクーヘン 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885903640"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2Mzk2NTY4LCJsYXN0X3ZhbHVlIjo5NTkyNjU2Mzk2NTY4fQ==",
                    "node": {
                        "title": "なだ万監修 絆のかさなりバウムクーヘン詰め合せ 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464885969176"
                                    }
                                }
                            ]
                        }
                    }
                },
                {
                    "cursor": "eyJsYXN0X2lkIjo5NTkyNjU2NDYyMTA0LCJsYXN0X3ZhbHVlIjo5NTkyNjU2NDYyMTA0fQ==",
                    "node": {
                        "title": "六本木アマンド 六本木チーズミルフィーユ 【結婚式　ギフト　食品　引き菓子　洋菓子】",
                        "variants": {
                            "edges": [
                                {
                                    "node": {
                                        "id": "gid://shopify/ProductVariant/49464886034712"
                                    }
                                }
                            ]
                        }
                    }
                }
            ]
        }
    },
    "extensions": {
        "cost": {
            "requestedQueryCost": 79,
            "actualQueryCost": 22,
            "throttleStatus": {
                "maximumAvailable": 4000,
                "currentlyAvailable": 3978,
                "restoreRate": 200
            }
        },
        "search": [
            {
                "path": [
                    "products"
                ],
                "query": "9592655905048 OR 9592656101656 OR 9592656003352 OR 9592656068888 OR 9592656167192 OR 9592656232728 OR 9592656462104 OR 9592656396568 OR 9592655937816 OR 9592656363800 OR 9592655544600 OR 9592655642904 OR 9592655806744 OR 9592655839512 OR 9592655675672 OR 9592655872280 OR 9592656036120",
                "parsed": {
                    "or": [
                        {
                            "field": "default",
                            "match_all": "9592655905048"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656101656"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656003352"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656068888"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656167192"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656232728"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656462104"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656396568"
                        },
                        {
                            "field": "default",
                            "match_all": "9592655937816"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656363800"
                        },
                        {
                            "field": "default",
                            "match_all": "9592655544600"
                        },
                        {
                            "field": "default",
                            "match_all": "9592655642904"
                        },
                        {
                            "field": "default",
                            "match_all": "9592655806744"
                        },
                        {
                            "field": "default",
                            "match_all": "9592655839512"
                        },
                        {
                            "field": "default",
                            "match_all": "9592655675672"
                        },
                        {
                            "field": "default",
                            "match_all": "9592655872280"
                        },
                        {
                            "field": "default",
                            "match_all": "9592656036120"
                        }
                    ]
                }
            }
        ]
    }
}
const obj = {
    "id": "gid://shopify/DeliveryProfile/124215492888",
    "profile": {
        "variantsToAssociate": []
    }
}

const variants = [];
const test = data.data.products.edges.forEach((item) => {
    const variant = item.node.variants.edges[0].node.id;
    variants.push(variant);
});
console.log(variants.length);

obj.profile.variantsToAssociate = variants;

console.log(JSON.stringify(variants));
