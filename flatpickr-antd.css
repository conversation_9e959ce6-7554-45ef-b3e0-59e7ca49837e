/* Ant Design Inspired Flatpickr Styles */

:root {
  /* Ant Design Color Palette */
  --antd-primary: #1677ff;
  --antd-primary-hover: #4096ff;
  --antd-primary-active: #0958d9;
  --antd-primary-bg: #e6f4ff;
  --antd-primary-bg-hover: #bae0ff;
  
  --antd-text: rgba(0, 0, 0, 0.88);
  --antd-text-secondary: rgba(0, 0, 0, 0.65);
  --antd-text-tertiary: rgba(0, 0, 0, 0.45);
  --antd-text-quaternary: rgba(0, 0, 0, 0.25);
  
  --antd-border: #d9d9d9;
  --antd-border-secondary: #f0f0f0;
  --antd-bg-container: #ffffff;
  --antd-bg-elevated: #ffffff;
  --antd-bg-layout: #f5f5f5;
  
  --antd-success: #52c41a;
  --antd-warning: #faad14;
  --antd-error: #ff4d4f;
  
  --antd-radius-base: 6px;
  --antd-radius-sm: 4px;
  --antd-radius-lg: 8px;
  
  --antd-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  --antd-shadow-base: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

/* Flatpickr Calendar Container */
.flatpickr-calendar.antd-theme {
  background: var(--antd-bg-elevated) !important;
  border: 1px solid var(--antd-border) !important;
  border-radius: var(--antd-radius-lg) !important;
  box-shadow: var(--antd-shadow-base) !important;
  padding: 12px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5715 !important;
}

/* Calendar Header */
.flatpickr-calendar.antd-theme .flatpickr-months {
  padding-bottom: 8px !important;
  border-bottom: 1px solid var(--antd-border-secondary) !important;
  margin-bottom: 8px !important;
}

.flatpickr-calendar.antd-theme .flatpickr-month {
  background: transparent !important;
  color: var(--antd-text) !important;
}

.flatpickr-calendar.antd-theme .flatpickr-current-month {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: var(--antd-text) !important;
  padding: 0 !important;
}

.flatpickr-calendar.antd-theme .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: var(--antd-bg-container) !important;
  border: 1px solid var(--antd-border) !important;
  border-radius: var(--antd-radius-base) !important;
  color: var(--antd-text) !important;
  font-weight: 400 !important;
  padding: 4px 8px !important;
}

.flatpickr-calendar.antd-theme .numInputWrapper {
  background: var(--antd-bg-container) !important;
  border: 1px solid var(--antd-border) !important;
  border-radius: var(--antd-radius-base) !important;
  padding: 4px 8px !important;
}

.flatpickr-calendar.antd-theme .numInputWrapper input {
  color: var(--antd-text) !important;
  font-weight: 400 !important;
}

/* Navigation Arrows */
.flatpickr-calendar.antd-theme .flatpickr-prev-month,
.flatpickr-calendar.antd-theme .flatpickr-next-month {
  width: 28px !important;
  height: 28px !important;
  border-radius: var(--antd-radius-base) !important;
  background: transparent !important;
  border: 1px solid transparent !important;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--antd-text-tertiary) !important;
}

.flatpickr-calendar.antd-theme .flatpickr-prev-month:hover,
.flatpickr-calendar.antd-theme .flatpickr-next-month:hover {
  background: var(--antd-primary-bg) !important;
  border-color: var(--antd-primary-hover) !important;
  color: var(--antd-primary-hover) !important;
}

.flatpickr-calendar.antd-theme .flatpickr-prev-month svg,
.flatpickr-calendar.antd-theme .flatpickr-next-month svg {
  width: 12px !important;
  height: 12px !important;
  fill: currentColor !important;
}

/* Weekdays */
.flatpickr-calendar.antd-theme .flatpickr-weekdays {
  background: transparent !important;
  margin: 8px 0 !important;
}

.flatpickr-calendar.antd-theme .flatpickr-weekday {
  background: transparent !important;
  color: var(--antd-text-secondary) !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: 8px 4px !important;
  text-align: center !important;
  border-bottom: 1px solid var(--antd-border-secondary) !important;
}

/* Calendar Days */
.flatpickr-calendar.antd-theme .flatpickr-days {
  width: 100% !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day {
  background: transparent !important;
  border: 1px solid transparent !important;
  border-radius: var(--antd-radius-base) !important;
  color: var(--antd-text) !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  height: 32px !important;
  width: 32px !important;
  line-height: 30px !important;
  margin: 1px !important;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
  cursor: pointer !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day:hover {
  background: var(--antd-primary-bg) !important;
  border-color: var(--antd-primary-hover) !important;
  color: var(--antd-primary-hover) !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day.selected {
  background: var(--antd-primary) !important;
  border-color: var(--antd-primary) !important;
  color: #ffffff !important;
  font-weight: 400 !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day.selected:hover {
  background: var(--antd-primary-hover) !important;
  border-color: var(--antd-primary-hover) !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day.today {
  background: transparent !important;
  border-color: var(--antd-primary) !important;
  color: var(--antd-primary) !important;
  font-weight: 400 !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day.today:hover {
  background: var(--antd-primary-bg) !important;
  border-color: var(--antd-primary-hover) !important;
  color: var(--antd-primary-hover) !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day.prevMonthDay,
.flatpickr-calendar.antd-theme .flatpickr-day.nextMonthDay {
  color: var(--antd-text-quaternary) !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day.disabled {
  color: var(--antd-text-quaternary) !important;
  cursor: not-allowed !important;
  background: transparent !important;
  opacity: 0.5 !important;
}

.flatpickr-calendar.antd-theme .flatpickr-day.disabled:hover {
  background: transparent !important;
  border-color: transparent !important;
  color: var(--antd-text-quaternary) !important;
  cursor: not-allowed !important;
}

/* Input Field Styling */
.antd-datepicker-input {
  width: 100%;
  padding: 4px 11px;
  font-size: 14px;
  line-height: 1.5715;
  color: var(--antd-text);
  background-color: var(--antd-bg-container);
  border: 1px solid var(--antd-border);
  border-radius: var(--antd-radius-base);
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  box-sizing: border-box;
}

.antd-datepicker-input:hover {
  border-color: var(--antd-primary-hover);
}

.antd-datepicker-input:focus {
  border-color: var(--antd-primary);
  box-shadow: 0 0 0 2px var(--antd-primary-bg);
  outline: none;
}

.antd-datepicker-input::placeholder {
  color: var(--antd-text-tertiary);
}

/* Animation */
.flatpickr-calendar.antd-theme.open {
  animation: antdFadeIn 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

@keyframes antdFadeIn {
  from {
    opacity: 0;
    transform: scaleY(0.8);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  :root {
    --antd-text: rgba(255, 255, 255, 0.85);
    --antd-text-secondary: rgba(255, 255, 255, 0.65);
    --antd-text-tertiary: rgba(255, 255, 255, 0.45);
    --antd-text-quaternary: rgba(255, 255, 255, 0.25);
    --antd-border: #424242;
    --antd-border-secondary: #303030;
    --antd-bg-container: #1f1f1f;
    --antd-bg-elevated: #262626;
    --antd-bg-layout: #000000;
  }
}
