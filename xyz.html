<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <form action="/test/signin">
        <input type="text" name="username" placeholder="Username">
        <input type="password" name="password" placeholder="Password">
        <button type="submit">Click Element</button>
    </form>
    <script>
        document.querySelector('button').addEventListener('click', function(e) {
            e.preventDefault();
            const username = document.querySelector('input[name="username"]').value;
            const password = document.querySelector('input[name="password"]').value;
            console.log({ username, password });
            fetch('/url', {
                body: JSON.stringify({ username, password }),
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
        });
    </script>
</body>
</html>