# React DatePicker Library

A comprehensive React datepicker component with time slot support, built with React.js and Tailwind CSS.

## Features

- 🗓️ **Date Selection** - Interactive calendar with month/year navigation
- ⏰ **Time Slots** - Configurable time ranges (e.g., 7:00 AM - 1:00 PM)
- 🎨 **Multiple Variants** - flat, bordered, faded, underlined styles
- 📱 **Responsive Design** - Mobile-friendly with touch support
- ♿ **Accessibility** - Keyboard navigation and screen reader support
- 🚫 **Date Validation** - Disable past dates, weekends, custom ranges
- 🎯 **Flexible Configuration** - Customizable time ranges and intervals
- 🎨 **Tailwind CSS** - Fully styled with Tailwind utility classes

## Installation

1. **Install dependencies:**
```bash
npm install
```

2. **Start development server:**
```bash
npm run dev
```

3. **Build for production:**
```bash
npm run build
```

## Usage

### Basic DatePicker

```jsx
import DatePicker from './DatePicker';

function App() {
  const handleDateChange = (value) => {
    console.log('Selected:', value);
  };

  return (
    <DatePicker
      variant="bordered"
      size="md"
      label="Select Date"
      placeholder="Choose a date"
      onChange={handleDateChange}
    />
  );
}
```

### With Time Slots

```jsx
<DatePicker
  variant="bordered"
  timeSlots={true}
  timeSlotStart={7}        // 7 AM
  timeSlotEnd={13}         // 1 PM
  timeSlotInterval={30}    // 30-minute intervals
  disablePastDates="true"
  label="Appointment Time"
  placeholder="Select appointment slot"
  description="Available: 7:00 AM - 1:00 PM"
  onChange={handleDateChange}
/>
```

### Advanced Configuration

```jsx
<DatePicker
  variant="faded"
  size="lg"
  timeSlots={true}
  timeSlotStart={9}
  timeSlotEnd={17}
  timeSlotInterval={60}
  disablePastDates="including-today"
  disabledDates="weekends"
  required={true}
  label="Business Meeting"
  placeholder="Select meeting time"
  description="Business hours only, no weekends"
  onChange={handleDateChange}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'flat' \| 'bordered' \| 'faded' \| 'underlined'` | `'flat'` | Input style variant |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Component size |
| `color` | `'default' \| 'primary' \| 'success' \| 'warning' \| 'danger'` | `'default'` | Color theme |
| `showTime` | `boolean` | `false` | Show time picker |
| `timeSlots` | `boolean` | `false` | Show predefined time slots |
| `timeSlotStart` | `number` | `7` | Start hour (24-hour format) |
| `timeSlotEnd` | `number` | `13` | End hour (24-hour format) |
| `timeSlotInterval` | `number` | `30` | Interval in minutes |
| `disablePastDates` | `'true' \| 'including-today' \| 'false'` | `'true'` | Disable past dates |
| `disabledDates` | `'weekends' \| null` | `null` | Disable specific date patterns |
| `required` | `boolean` | `false` | Mark as required field |
| `label` | `string` | `''` | Input label |
| `placeholder` | `string` | `'Select date'` | Input placeholder |
| `description` | `string` | `''` | Helper text |
| `onChange` | `function` | `() => {}` | Change callback |
| `value` | `Date \| null` | `null` | Controlled value |

## Time Range Examples

### Morning Appointments (7 AM - 1 PM)
```jsx
<DatePicker
  timeSlots={true}
  timeSlotStart={7}
  timeSlotEnd={13}
  timeSlotInterval={30}
/>
```

### Business Hours (9 AM - 5 PM)
```jsx
<DatePicker
  timeSlots={true}
  timeSlotStart={9}
  timeSlotEnd={17}
  timeSlotInterval={60}
/>
```

### Doctor Appointments (8 AM - 12 PM)
```jsx
<DatePicker
  timeSlots={true}
  timeSlotStart={8}
  timeSlotEnd={12}
  timeSlotInterval={15}
/>
```

### Evening Classes (6 PM - 10 PM)
```jsx
<DatePicker
  timeSlots={true}
  timeSlotStart={18}
  timeSlotEnd={22}
  timeSlotInterval={120}
/>
```

## Styling

The component uses Tailwind CSS classes and can be customized by:

1. **Modifying the CSS file** (`DatePickerReact.css`)
2. **Extending Tailwind config** (`tailwind.config.js`)
3. **Passing custom className** prop

## Dependencies

- React 18+
- @heroicons/react (for icons)
- Tailwind CSS 3+

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

MIT License
