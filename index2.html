<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
    font-family: Arial, sans-serif;
}

#slidingDiv {
    background-color: #f0f0f0;
    padding: 20px;
    border: 1px solid #ccc;
    margin-top: 10px;
    transition: max-height 0.5s ease-in-out, padding 0.5s ease-in-out;
    max-height: 0; /* Collapsed by default */
    overflow: hidden; /* Prevent content overflow */
}

.hidden {
    max-height: 0;
    padding: 0;
}

.visible {
    max-height: 200px; /* Set a max height for the visible state */
    padding: 20px;
}

    </style>
</head>
<body>
    <button id="toggleButton">Toggle Slide</button>
    <div id="slidingDiv" class="hidden">
        <h2>This is a sliding div!</h2>
        <p>Content goes here...</p>
    </div>

    <script>
        const slidingDiv = document.getElementById('slidingDiv');

// Function to toggle slide
function toggleSlide() {
    if (slidingDiv.classList.contains('hidden')) {
        slidingDiv.classList.remove('hidden');
        slidingDiv.classList.add('visible');
    } else {
        slidingDiv.classList.remove('visible');
        slidingDiv.classList.add('hidden');
    }
}

// Automatically slide down after 3 seconds
setTimeout(() => {
    toggleSlide();

    // Automatically slide up after 3 seconds
    setTimeout(() => {
        toggleSlide();
    }, 3000);
}, 3000);

    </script>
</body>
</html>