/**
 * DatePicker Library - HeroUI Style with Flatpickr
 * A comprehensive datepicker with time slot support using Flatpickr
 */

class DatePickerWithFlatpickr {
    constructor(element, options = {}) {
        this.element = element;
        this.options = {
            variant: element.dataset.variant || 'flat',
            size: element.dataset.size || 'md',
            disablePastDates: true,
            timeSlots: true,
            ...options
        };

        this.selectedDate = null;
        this.selectedTimeSlot = null;
        this.flatpickrInstance = null;

        // Time slots data
        this.timeSlots = [
            { time: '7:00 AM - 10:00 AM', available: true },
            { time: '10:00 AM - 1:00 PM', available: true }
        ];

        this.init();
    }

    init() {
        this.input = this.element.querySelector('.datepicker-input');
        this.trigger = this.element.querySelector('.datepicker-trigger');
        this.timeSlotsContainer = this.element.querySelector('.time-slots-container');

        this.initFlatpickr();
        this.bindTimeSlotEvents();
    }

    initFlatpickr() {
        const self = this;

        this.flatpickrInstance = flatpickr(this.input, {
            dateFormat: "m/d/Y",
            minDate: this.options.disablePastDates ? "today" : null,
            clickOpens: true,
            allowInput: false,
            position: "auto center",
            onOpen: function(selectedDates, dateStr, instance) {
                // Show time slots when calendar opens and date is selected
                if (selectedDates.length > 0) {
                    self.showTimeSlots();
                }
            },
            onChange: function(selectedDates, dateStr, instance) {
                if (selectedDates.length > 0) {
                    self.selectedDate = selectedDates[0];
                    self.showTimeSlots();
                    // Clear previous time slot selection
                    self.selectedTimeSlot = null;
                    self.updateTimeSlotsDisplay();
                } else {
                    self.hideTimeSlots();
                }
            },
            onClose: function(selectedDates, dateStr, instance) {
                // Update input value with date and time slot if both selected
                self.updateInputValue();
            }
        });

        // Custom trigger button functionality
        this.trigger.addEventListener('click', () => {
            this.flatpickrInstance.open();
        });
    }

    bindTimeSlotEvents() {
        const timeSlotButtons = this.timeSlotsContainer.querySelectorAll('.time-slot');

        timeSlotButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.selectTimeSlot(button.dataset.time, button);
            });
        });
    }

    showTimeSlots() {
        if (this.timeSlotsContainer) {
            this.timeSlotsContainer.classList.remove('hidden');
        }
    }

    hideTimeSlots() {
        if (this.timeSlotsContainer) {
            this.timeSlotsContainer.classList.add('hidden');
        }
    }

    selectTimeSlot(time, element) {
        // Remove previous selection
        this.timeSlotsContainer.querySelectorAll('.time-slot.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Add selection to clicked element
        element.classList.add('selected');
        this.selectedTimeSlot = time;

        // Update input value immediately
        this.updateInputValue();

        // Close flatpickr after time slot selection
        setTimeout(() => {
            this.flatpickrInstance.close();
        }, 300);
    }

    updateTimeSlotsDisplay() {
        // Clear all selections when date changes
        this.timeSlotsContainer.querySelectorAll('.time-slot.selected').forEach(el => {
            el.classList.remove('selected');
        });
    }

    updateInputValue() {
        if (this.selectedDate) {
            let dateString = this.formatDate(this.selectedDate);

            if (this.selectedTimeSlot) {
                dateString += ' ' + this.selectedTimeSlot;
            }

            this.input.value = dateString;

            // Update demo display if it exists
            const selectedValueDisplay = document.getElementById('selected-value');
            if (selectedValueDisplay) {
                selectedValueDisplay.textContent = dateString;
            }

            // Trigger custom event
            const event = new CustomEvent('datechange', {
                detail: {
                    date: this.selectedDate,
                    timeSlot: this.selectedTimeSlot,
                    formatted: dateString
                }
            });
            this.element.dispatchEvent(event);
        }
    }

    formatDate(date) {
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${month}/${day}/${year}`;
    }

    // Utility methods for external use
    setValue(date, timeSlot = null) {
        this.flatpickrInstance.setDate(date);
        this.selectedDate = new Date(date);
        this.selectedTimeSlot = timeSlot;

        if (timeSlot) {
            // Select the time slot button
            const timeSlotButton = this.timeSlotsContainer.querySelector(`[data-time="${timeSlot}"]`);
            if (timeSlotButton) {
                this.selectTimeSlot(timeSlot, timeSlotButton);
            }
        }

        this.updateInputValue();
    }

    getValue() {
        return {
            date: this.selectedDate,
            timeSlot: this.selectedTimeSlot,
            formatted: this.input.value
        };
    }

    clear() {
        this.flatpickrInstance.clear();
        this.selectedDate = null;
        this.selectedTimeSlot = null;
        this.input.value = '';
        this.hideTimeSlots();

        const selectedValueDisplay = document.getElementById('selected-value');
        if (selectedValueDisplay) {
            selectedValueDisplay.textContent = 'No date selected';
        }
    }

    destroy() {
        if (this.flatpickrInstance) {
            this.flatpickrInstance.destroy();
        }
    }
}

// Initialize all datepickers when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const datepickers = document.querySelectorAll('.datepicker');

    datepickers.forEach(element => {
        new DatePickerWithFlatpickr(element);
    });

    // Add keyboard navigation for Flatpickr
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close any open Flatpickr instances
            const openCalendars = document.querySelectorAll('.flatpickr-calendar.open');
            openCalendars.forEach(calendar => {
                const instance = calendar._flatpickr;
                if (instance) {
                    instance.close();
                }
            });
        }
    });
});


// Export for use as module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DatePickerWithFlatpickr };
}

// Make available globally
window.DatePickerWithFlatpickr = DatePickerWithFlatpickr;
