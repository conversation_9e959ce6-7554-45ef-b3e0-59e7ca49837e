<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新規会員登録</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            background-color: #f9f9f9;
        }

        .form-container {
            background-color: #fff;
            border-radius: 8px;
            padding: 30px;
            width: 450px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        h2 {
            text-align: center;
            color: #00a2ff;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group input[type="radio"],
        .form-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        .inline-group {
            display: flex;
            gap: 10px;
        }

        .form-group .info-text {
            font-size: 0.85em;
            color: #666;
            margin-top: 8px;
        }

        .form-group .required {
            color: red;
            font-size: 0.9em;
        }

        .btn-submit {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 5px;
            background-color: #00a2ff;
            color: #fff;
            font-size: 16px;
            cursor: pointer;
            margin-top: 15px;
        }

        .btn-submit:hover {
            background-color: #008bdb;
        }

        .form-link {
            color: #00a2ff;
            text-decoration: none;
            margin-left: 5px;
        }

        .form-link:hover {
            text-decoration: underline;
        }
        .btn-submit {
            display: none; /* Hide button by default */
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 5px;
            background-color: #00a2ff;
            color: #fff;
            font-size: 16px;
            cursor: pointer;
            margin-top: 15px;
        }

        .btn-submit.visible {
            display: block; /* Show when all fields are valid */
        }

        .error-message {
            color: red;
            font-size: 0.85em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>新規会員登録</h2>
        <!-- Custom Form Element -->
        <custom-form>
            <form>
                <!-- Name -->
                <div class="form-group">
                    <label>お名前 <span class="required">(必須)</span></label>
                    <div class="inline-group">
                        <input type="text" placeholder="姓" data-required>
                        <input type="text" placeholder="名" data-required>
                    </div>
                </div>

                <!-- Kana Name -->
                <div class="form-group">
                    <label>お名前カナ <span class="required">(必須)</span></label>
                    <input type="text" placeholder="姓カナ" data-required>
                </div>

                <!-- Email -->
                <div class="form-group">
                    <label>メールアドレス <span class="required">(必須)</span></label>
                    <input type="email" placeholder="例: <EMAIL>" data-required data-email>
                </div>

                <!-- Password -->
                <div class="form-group">
                    <label>パスワード <span class="required">(必須)</span></label>
                    <input type="password" placeholder="パスワードを入力" data-required>
                </div>

                <!-- Birthdate -->
                <div class="form-group">
                    <label>ご生年月日 <span class="required">(必須)</span></label>
                    <input type="date" data-required>
                </div>

                <!-- Preferred Visit Date -->
                <div class="form-group">
                    <label>ご利用予定月 <span class="required">(必須)</span></label>
                    <input type="date" data-required>
                </div>

                <!-- Gender -->
                <div class="form-group">
                    <label>性別 <span class="required">(必須)</span></label>
                    <div>
                        <input type="radio" name="gender" value="male" data-required> 男性
                        <input type="radio" name="gender" value="female" data-required> 女性
                    </div>
                </div>

                <!-- Terms and Privacy Agreement -->
                <div class="form-group">
                    <input type="checkbox" data-required> <label> <a href="#" class="form-link">ご利用規約に同意する</a></label><br>
                    <input type="checkbox" data-required> <label> <a href="#" class="form-link">個人情報保護方針について同意する</a></label>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="btn-submit">会員登録する</button>
            </form>
        </custom-form>
    </div>

    <script>
        class CustomForm extends HTMLElement {
            constructor() {
                super();
                this.form = this.querySelector('form');
                this.requiredFields = this.querySelectorAll('[data-required]');
                this.submitButton = this.form.querySelector('.btn-submit');
            }

            connectedCallback() {
                this.requiredFields.forEach(field => {
                    field.addEventListener('change', this.validateField.bind(this));
                });
                this.form.addEventListener('submit', this.handleSubmit.bind(this));
            }

            validateField(event) {
                const field = event.target;
                this.clearError(field);
                // Email validation
                if (field.hasAttribute('data-email')) {
                    if (!this.isValidEmail(field.value)) {
                        this.showError(field, "メールアドレスの形式が正しくありません。");
                    }
                } else if (field.type === 'checkbox' || field.type === 'radio') {
                    if (!this.isGroupValid(field)) {
                        this.showError(field, "この項目は必須です。");
                    }
                } else if (!field.value.trim()) {
                    this.showError(field, "この項目は必須です。");
                }

                // Check if all fields are valid to show the submit button
                this.updateSubmitButtonVisibility();
            }

            isValidEmail(email) {
                // Simple regex for basic email validation
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailPattern.test(email);
            }

            isGroupValid(field) {
                const name = field.name;
                if (name) {
                    return Array.from(this.form.querySelectorAll(`[name="${name}"]`)).some(input => input.checked);
                }
                return field.checked;
            }

            updateSubmitButtonVisibility() {
                const allValid = Array.from(this.requiredFields).every(field => {
                    if (field.hasAttribute('data-email')) {
                        return this.isValidEmail(field.value);
                    }
                    if (field.type === 'checkbox' || field.type === 'radio') {
                        return this.isGroupValid(field);
                    }
                    return field.value.trim();
                });

                if (allValid) {
                    this.submitButton.classList.add('visible');
                } else {
                    this.submitButton.classList.remove('visible');
                }
            }

            showError(field, message) {
                const errorText = document.createElement('div');
                errorText.className = 'error-message';
                errorText.textContent = message;
                field.parentElement.appendChild(errorText);
            }

            clearError(field) {
                const error = field.parentElement.querySelector('.error-message');
                if (error) error.remove();
            }

            handleSubmit(event) {
                if (!this.submitButton.classList.contains('visible')) {
                    event.preventDefault();
                }
            }
        }

        // Define the custom form element
        customElements.define('custom-form', CustomForm);
    </script>
</body>
</html>
