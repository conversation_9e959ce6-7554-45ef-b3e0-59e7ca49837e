<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <div>
        <meta charset="utf-8">平素よりMUUUをご利用いただき、誠にありがとうございます。
    </div>
    <div>この度、サービス向上のため、商品の発送予定日の表記変更を予定しております。</div>
    <div>これまで「発送予定：◯ 〜 ◯週間 」と表記しておりましたが、お客様のご注文イメージをより明確にするために、「発送予定：◯月上旬」等の表記を追加させていただきます。詳細は<strong><span
                style="color: #34aade;"><a
                    href="https://stg-renewal.myshopify.com/blogs/news/h1-stores-%E3%83%96%E3%83%A9%E3%83%B3%E3%83%89%E3%82%A2%E3%83%97%E3%83%AA%E3%81%AB-%E3%83%A2%E3%83%BC%E3%83%AB%E5%BA%97%E8%88%97%E3%81%AE%E3%83%9D%E3%82%A4%E3%83%B3%E3%83%88%E3%82%92%E7%A7%BB%E8%A1%8C%E3%81%99%E3%82%8B"
                    title="こちら。" style="color: #34aade;"
                    data-mce-href="https://stg-renewal.myshopify.com/blogs/news/h1-stores-%E3%83%96%E3%83%A9%E3%83%B3%E3%83%89%E3%82%A2%E3%83%97%E3%83%AA%E3%81%AB-%E3%83%A2%E3%83%BC%E3%83%AB%E5%BA%97%E8%88%97%E3%81%AE%E3%83%9D%E3%82%A4%E3%83%B3%E3%83%88%E3%82%92%E7%A7%BB%E8%A1%8C%E3%81%99%E3%82%8B">こちら</a></span></strong>。
    </div>
    <p>つきましては、誠に勝手ながら、下記期間に変更作業を行わせていただきます。</p>
    <div><strong>変更箇所：商品ページ、購入履歴に記載されている発送予定日の表記</strong></div>
    <div><strong>変更作業期間：2024年7月29日(月) 11:00~12:00</strong></div>
    <p>なお、表記変更のみとなりますので、商品の発送予定時期に変更はなく、お客様に直接的な影響はございません。<br>お客様にはご迷惑をおかけいたしますが、ご理解賜りますよう、よろしくお願い申し上げます。</p>
    <p>MUUU</p>
    <p><strong>&lt;H2&gt;STORES ブランドアプリに、他店舗のポイントを一括で移行する方法</strong></p>
    <div style="display: flex;" data-mce-style="display: flex;" class="row-to-col-mobile">
        <div style="min-width: 200px; border-right: 1px solid #ccc;"
            data-mce-style="min-width: 200px; border-right: 1px solid #ccc;"><b>会場</b></div>
        <div style="display: flex; flex-direction: column; padding-left: 20px;"
            data-mce-style="display: flex; flex-direction: column; padding-left: 20px;"><span>Hall Mixa</span> <span
                style="color: #34aade;"><a style="color: #34aade;"
                    href="https://hall.mixalivetokyo.com/">https://hall.mixalivetokyo.com/</a></span>
            <span>東京都豊島区東池袋1-14-3 Mixalive TOKYO B2F</span> <span>※ 会場へのお問い合わせはご遠慮ください。</span> <span>※
                祝い花、フラワースタンド等の受取は運営スペース確保の都合により、お断りをさせていただいております。</span>
        </div>
    </div>
    <div style="display: flex; margin-top: 20px;" data-mce-style="display: flex; margin-top: 20px;"
        class="row-to-col-mobile">
        <div style="min-width: 200px; border-right: 1px solid #ccc;"
            data-mce-style="min-width: 200px; border-right: 1px solid #ccc;"><b>チケット料金</b></div>
        <div style="display: flex; flex-direction: column; padding-left: 20px;"
            data-mce-style="display: flex; flex-direction: column; padding-left: 20px;">
            <span>全席指定　¥4,000(税込)</span>
            ※チケット代金以外の諸手数料が別途かかります。
        </div>
    </div>

    <div style="display: flex; margin-top: 20px;" data-mce-style="display: flex; margin-top: 20px;"
        class="row-to-col-mobile">
        <div style="min-width: 200px; border-right: 1px solid #ccc;"
            data-mce-style="min-width: 200px; border-right: 1px solid #ccc;"><b>出演者</b></div>
        <div style="display: flex; flex-direction: column; padding-left: 20px;"
            data-mce-style="display: flex; flex-direction: column; padding-left: 20px;"><span>田中みかん</span></div>
    </div>
    <style>
        @media screen and (max-width: 500px) {
            .row-to-col-mobile {
                flex-direction: column;
            }

            .row-to-col-mobile>div {
                border-right: none !important;
            }

            .row-to-col-mobile div:last-child {
                padding-left: 0 !important;
                margin: 0;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
                padding-top: 10px;
            }
        }
    </style>
    <p>テープスでは、ブロックを組み合わせて「フレーズ」と呼ばれるワークフローを構築し、業務を自動化します。フレーズのテンプレートを利用すれば、設定もかんたんです。</p>
    <p><strong>&lt;H3&gt;Google スプレッドシートを準備する</strong></p>
    <hr>
    <p>テープスでは、ブロックを組み合わせて「フレーズ」と呼ばれるワークフローを構築し、業務を自動化します。フレーズのテンプレートを利用すれば、設定もかんたんです。</p>
    <p>今回は「STORES ブランドアプリ｜Google スプレッドシートをもとに、会員にポイントを付与する」というテンプレートを利用します。</p>
    <ul>
        <li>Google スプレッドシートを読み取り、情報を取得</li>
        <li>シートの情報にもとづき、STORES
            ブランドアプリで会員にポイントを付与サンプルテキストサンプルテキストサンプルテキストサンプルテキストサンプルテキストサンプルテキストサンプルテキストサンプルテキストサンプルテキストサンプル</li>
    </ul>
    <div style="padding: 20px; background: #fff; border-radius: 5px;">テープスにより、STORES
        ブランドアプリ会員へのポイントの一括付与が実現します。これにより、他店舗からのポイント移行や、幅広いキャンペーン施策が可能になります。</div>
    <a class="btn-link" href="/"><span>詳細はこちら</span><span><svg fill="none" viewBox="0 0 14 12" height="12" width="14"
                class="icon icon-arrow-right {{ class }}" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                focusable="false">
                <path stroke-miterlimit="10" stroke="#222222" d="M7.65869 0.282227L13.4229 5.99613L7.65869 11.71">
                </path>
                <path stroke-miterlimit="10" stroke="#222222" d="M0 5.99609H13.4228"></path>
            </svg></span></a> <br><br><br>
</body>

</html>