class ValidateFormElement extends HTMLElement {
    constructor() {
        super();
        this.currentFormId = null;
        this.validateType = {
            key_max_length: [],
            email: false,
            phone: false,
            zip: false,
            password_confirm: false,
            birth_day: false
        };
    }

    showMessage(input, field, message) {
        input.classList.add('invalid');

        const errorMessage = field.querySelector(`[data-cf-field="${input.name}"]`);
        if (errorMessage) {
            errorMessage.style.display = 'block';
            errorMessage.textContent = message;
        }
    }

    handleSubmit(e, cb) {
        e.preventDefault();

        const form = document.querySelector(`.${this.currentFormId}`);

        this.invalid = form.querySelectorAll('.invalid');
        for (const invalid of this.invalid) {
            invalid.classList.remove('invalid');
        }
        this.errorMessages = form.querySelectorAll('.unico-m-form-invalid');
        for (const errorMessage of this.errorMessages) {
            errorMessage.style.display = 'none';
            errorMessage.textContent = '';
        }

        const inputs = form.querySelectorAll('input, select');
        for (const input of inputs) {
            input.setAttribute('aria-invalid', false);
        }

        this.validation(form);
        this.submitEvt(form, cb);
    }

    submitEvt(form, cb) {
        if (form.querySelectorAll('.invalid').length > 0) return;
        cb && cb();
    }

    validation(form) {
        const requiredFields = form.querySelectorAll('[data-required]');
        for (const field of requiredFields) {
            if (field.classList.contains('disabled')) continue;

            const inputs = field.querySelectorAll('input, select');
            for (const input of inputs) {
                if (input && !input.value) {
                    this.showMessage(input, field, "{{- 'weblife.section.register.validation.required' | t -}}")
                }

                if (this.validateType['key_max_length'].some(key => input.dataset.validation === key)) {
                    if (input.value.length > 255) {
                        this.showMessage(input, field, "{{- 'weblife.section.register.validation.max_length_255' | t -}}")
                    }
                }

                if (this.validateType['email'] && input.dataset.validation === 'email') {
                    const emailRegex = /^[a-zA-Z0-9_.+-]+@([a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.)+[a-zA-Z]{2,}$/;
                    if (!emailRegex.test(input.value)) {
                        this.showMessage(input, field, "{{- 'weblife.section.register.validation.email' | t -}}")
                    }
                }
                if (this.validateType['phone'] && input.dataset.validation === 'phone') {
                    const phoneRegex = /^\d{10,11}$/;
                    if (input.value && !phoneRegex.test(input.value)) {
                        this.showMessage(input, field, "{{- 'weblife.section.register.validation.phone' | t -}}")
                    }
                }
                if (this.validateType['zip'] && input.dataset.validation === 'zip') {
                    const zipRegex = /^\d{7}$/;
                    if (input.value && !zipRegex.test(input.value)) {
                        this.showMessage(input, field, "{{- 'weblife.section.register.validation.zip' | t -}}")
                    }
                }

                if (this.validateType['password_confirm'] && input.dataset.validation === 'password_confirm') {
                    form.querySelectorAll('input[type="password"]').forEach((password) => {
                        if (input.value && input.value !== password.value) {
                            this.showMessage(input, field, "{{- 'weblife.section.register.validation.password_not_match' | t -}}")
                        }
                    });
                }
            }
        }


        if (this.validateType['birth_day']) {
            /** Valiate date format (birthday) */
            const dateFields = form.querySelectorAll('[data-date-format]');
            for (const field of dateFields) {
                if (field.classList.contains('disabled')) continue;

                const selects = field.querySelectorAll('select');
                const isNeedValidate = Array.from(selects).some((select) => select.value);

                if (isNeedValidate) {
                    for (const select of selects) {
                        if (!select.value) {
                            select.classList.add('invalid');

                            const errorMessage = field.querySelector(`[data-cf-field="mf.unico._birthday"]`);
                            if (errorMessage) {
                                errorMessage.style.display = 'block';
                                errorMessage.textContent = "{{- 'weblife.section.register.validation.regex.birthday' | t -}}";
                            }
                        }
                    }
                }
            }
        }
    }
}
