import React, { useState } from 'react';
import DatePicker from './DatePicker';
import './DatePickerReact.css';

const App = () => {
  const [selectedValues, setSelectedValues] = useState({});

  const handleDateChange = (id, value) => {
    setSelectedValues(prev => ({
      ...prev,
      [id]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">
          React DatePicker Library
        </h1>
        <p className="text-gray-600 mb-12">
          A comprehensive datepicker library with time slot support, built with React.js and Tailwind CSS.
        </p>

        {/* Basic Examples */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Basic Usage</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Default DatePicker */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Default</h3>
              <DatePicker
                variant="flat"
                size="md"
                label="Select Date"
                placeholder="Select date"
                onChange={(value) => handleDateChange('default', value)}
              />
            </div>

            {/* With Time Slots */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">With Time Slots</h3>
              <DatePicker
                variant="bordered"
                size="md"
                showTime={true}
                timeSlots={true}
                label="Select Date & Time"
                placeholder="Select date and time"
                description="Available slots: 7:00 AM - 1:00 PM"
                onChange={(value) => handleDateChange('timeSlots', value)}
              />
            </div>

            {/* Required */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Required</h3>
              <DatePicker
                variant="faded"
                size="md"
                required={true}
                label="Birth Date"
                placeholder="Required field"
                onChange={(value) => handleDateChange('required', value)}
              />
            </div>
          </div>
        </section>

        {/* Variants */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Variants</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Flat</h3>
              <DatePicker
                variant="flat"
                placeholder="Flat variant"
                onChange={(value) => handleDateChange('flat', value)}
              />
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Bordered</h3>
              <DatePicker
                variant="bordered"
                placeholder="Bordered variant"
                onChange={(value) => handleDateChange('bordered', value)}
              />
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Faded</h3>
              <DatePicker
                variant="faded"
                placeholder="Faded variant"
                onChange={(value) => handleDateChange('faded', value)}
              />
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Underlined</h3>
              <DatePicker
                variant="underlined"
                placeholder="Underlined variant"
                onChange={(value) => handleDateChange('underlined', value)}
              />
            </div>
          </div>
        </section>

        {/* Sizes */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Sizes</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Small</h3>
              <DatePicker
                variant="bordered"
                size="sm"
                placeholder="Small size"
                onChange={(value) => handleDateChange('small', value)}
              />
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Medium</h3>
              <DatePicker
                variant="bordered"
                size="md"
                placeholder="Medium size"
                onChange={(value) => handleDateChange('medium', value)}
              />
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Large</h3>
              <DatePicker
                variant="bordered"
                size="lg"
                placeholder="Large size"
                onChange={(value) => handleDateChange('large', value)}
              />
            </div>
          </div>
        </section>

        {/* Advanced Features */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Advanced Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            
            {/* Morning Time Slots */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Morning Slots</h3>
              <DatePicker
                variant="bordered"
                timeSlots={true}
                timeSlotStart={7}
                timeSlotEnd={13}
                timeSlotInterval={30}
                disablePastDates="true"
                label="Morning Appointment"
                placeholder="Select morning slot"
                description="Available: 7:00 AM - 1:00 PM (30 min intervals)"
                onChange={(value) => handleDateChange('morning', value)}
              />
            </div>

            {/* Afternoon Time Slots */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Afternoon Slots</h3>
              <DatePicker
                variant="bordered"
                timeSlots={true}
                timeSlotStart={14}
                timeSlotEnd={18}
                timeSlotInterval={60}
                disablePastDates="true"
                label="Afternoon Appointment"
                placeholder="Select afternoon slot"
                description="Available: 2:00 PM - 6:00 PM (1 hour intervals)"
                onChange={(value) => handleDateChange('afternoon', value)}
              />
            </div>

            {/* No Past Dates */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Future Dates Only</h3>
              <DatePicker
                variant="bordered"
                disablePastDates="true"
                label="Future Dates"
                placeholder="Future dates only"
                description="Past dates are disabled"
                onChange={(value) => handleDateChange('future', value)}
              />
            </div>

            {/* No Weekends */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Business Days</h3>
              <DatePicker
                variant="bordered"
                disabledDates="weekends"
                label="Business Days Only"
                placeholder="Weekdays only"
                description="Weekends are disabled"
                onChange={(value) => handleDateChange('business', value)}
              />
            </div>
          </div>
        </section>

        {/* Time Range Configurations */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Time Range Configurations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Business Hours */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Business Hours</h3>
              <DatePicker
                variant="bordered"
                timeSlots={true}
                timeSlotStart={9}
                timeSlotEnd={17}
                timeSlotInterval={60}
                label="Business Meeting"
                placeholder="9 AM - 5 PM"
                description="9:00 AM - 5:00 PM (1 hour slots)"
                onChange={(value) => handleDateChange('business-hours', value)}
              />
            </div>

            {/* Doctor Hours */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Doctor Hours</h3>
              <DatePicker
                variant="bordered"
                timeSlots={true}
                timeSlotStart={8}
                timeSlotEnd={12}
                timeSlotInterval={15}
                label="Medical Appointment"
                placeholder="8 AM - 12 PM"
                description="8:00 AM - 12:00 PM (15 min slots)"
                onChange={(value) => handleDateChange('doctor', value)}
              />
            </div>

            {/* Evening Classes */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Evening Classes</h3>
              <DatePicker
                variant="bordered"
                timeSlots={true}
                timeSlotStart={18}
                timeSlotEnd={22}
                timeSlotInterval={120}
                label="Class Schedule"
                placeholder="6 PM - 10 PM"
                description="6:00 PM - 10:00 PM (2 hour slots)"
                onChange={(value) => handleDateChange('evening', value)}
              />
            </div>
          </div>
        </section>

        {/* Interactive Demo */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Interactive Demo</h2>
          <div className="bg-white p-8 rounded-lg shadow-sm border">
            <div className="max-w-md mx-auto">
              <DatePicker
                variant="bordered"
                size="lg"
                timeSlots={true}
                timeSlotStart={7}
                timeSlotEnd={13}
                timeSlotInterval={30}
                disablePastDates="true"
                label="Select Future Date and Time"
                placeholder="Click to select future date and time"
                description="Morning slots: 7:00 AM - 1:00 PM (30-minute intervals)"
                onChange={(value) => handleDateChange('demo', value)}
              />
              
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-700 mb-2">Selected Value:</h4>
                <p className="text-gray-600">
                  {selectedValues.demo?.formatted || 'No date selected'}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Selected Values Display */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">All Selected Values</h2>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <pre className="text-sm text-gray-600 overflow-auto">
              {JSON.stringify(selectedValues, null, 2)}
            </pre>
          </div>
        </section>
      </div>
    </div>
  );
};

export default App;
