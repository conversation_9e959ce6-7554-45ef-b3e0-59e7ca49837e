<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <ul>
        <li data-title="1">1</li>
        <li data-title="2">2</li>
        <li data-title="3">3</li>
        <li data-title="4">4</li>
        <li data-title="5">5</li>
    </ul>
    <script>
        document.querySelectorAll('li').forEach((li, index) => {
            li.addEventListener('click', () => {
                console.log('You clicked on li', index + 1);
            });
        });

        document.querySelectorAll('[data-title]').forEach((li, index) => {
            li.addEventListener('click', () => {
                console.log('You clicked on li', li.getAttribute('data-title'));
            });
        });
    </script>
</body>
</html>