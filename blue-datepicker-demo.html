<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blue Theme Datepicker Demo</title>
    
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <!-- Custom Blue Theme CSS -->
    <link rel="stylesheet" href="flatpickr-blue-theme.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .demo-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            max-width: 400px;
            width: 100%;
        }
        
        .demo-title {
            font-size: 28px;
            font-weight: 700;
            color: #000;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .demo-subtitle {
            font-size: 16px;
            color: #8E8E93;
            margin-bottom: 32px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #000;
            margin-bottom: 12px;
        }
        
        .demo-note {
            font-size: 14px;
            color: #8E8E93;
            margin-top: 24px;
            text-align: center;
            line-height: 1.6;
            padding: 16px;
            background: #F2F2F7;
            border-radius: 12px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 16px 0;
        }
        
        .feature-list li {
            padding: 4px 0;
            color: #8E8E93;
            font-size: 14px;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #007AFF;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Blue Theme Datepicker</h1>
        <p class="demo-subtitle">iOS-inspired design with blue accents</p>
        
        <div class="form-group">
            <label class="form-label" for="datepicker1">Select Date</label>
            <input type="text" id="datepicker1" class="blue-datepicker-input" placeholder="Choose a date...">
        </div>
        
        <div class="form-group">
            <label class="form-label" for="datepicker2">Select Date Range</label>
            <input type="text" id="datepicker2" class="blue-datepicker-input" placeholder="Choose date range...">
        </div>
        
        <div class="demo-note">
            <strong>Features:</strong>
            <ul class="feature-list">
                <li>Blue primary color (#007AFF)</li>
                <li>Rounded corners and smooth shadows</li>
                <li>Range selection with visual feedback</li>
                <li>Cancel/Apply buttons (for range mode)</li>
                <li>iOS-inspired design language</li>
            </ul>
            Click on the input fields to see the blue-themed datepicker in action!
        </div>
    </div>

    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <script>
        // Initialize single date picker
        const singlePicker = flatpickr("#datepicker1", {
            dateFormat: "F j, Y",
            defaultDate: "2021-06-11", // Set to match the image
            onReady: function(selectedDates, dateStr, instance) {
                // Add blue theme class to the calendar
                instance.calendarContainer.classList.add('blue-theme');
            }
        });
        
        // Initialize date range picker with Cancel/Apply buttons
        const rangePicker = flatpickr("#datepicker2", {
            mode: "range",
            dateFormat: "F j, Y",
            defaultDate: ["2021-06-11", "2021-06-24"], // Set range to match the image
            onReady: function(selectedDates, dateStr, instance) {
                // Add blue theme class to the calendar
                instance.calendarContainer.classList.add('blue-theme');
                
                // Add action buttons
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'blue-theme-actions';
                
                const cancelBtn = document.createElement('button');
                cancelBtn.className = 'blue-theme-btn cancel';
                cancelBtn.textContent = 'Cancel';
                cancelBtn.onclick = function() {
                    instance.close();
                };
                
                const applyBtn = document.createElement('button');
                applyBtn.className = 'blue-theme-btn apply';
                applyBtn.textContent = 'Apply';
                applyBtn.onclick = function() {
                    instance.close();
                };
                
                actionsDiv.appendChild(cancelBtn);
                actionsDiv.appendChild(applyBtn);
                instance.calendarContainer.appendChild(actionsDiv);
            }
        });
    </script>
</body>
</html>
