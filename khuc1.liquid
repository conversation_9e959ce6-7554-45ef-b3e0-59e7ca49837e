{% assign delivery_method_types = delivery_agreements | map: 'delivery_method_type' | uniq %}
{% if delivery_method_types.size > 1 %}
{% assign has_split_cart = true %}
{% else %}
{% assign has_split_cart = false %}
{% endif %}
{% capture email_title %}
{% if has_pending_payment %}
ご注文頂きありがとうございました。
{% else %}
ご購入頂きありがとうございました！
{% endif %}
{% endcapture %}
{% capture email_body %}
{% if has_pending_payment %}
{% if buyer_action_required %}
支払い完了後、確認メールが届きます。
{% else %}
決済が処理されています。注文が確認されるとメールが届きます。
{% endif %}
{% else %}
{% if requires_shipping %}
{% case delivery_method %}
{% when 'pick-up' %}
注文の受取の準備が整うと、メールが届きます。
{% when 'local' %}
{{ customer.last_name }}様様、ご注文の品を配達する準備を行っております。
{% else %}
注文の発送準備を行なっております。商品を発送いたしましたら、改めてお知らせいたします。
{% endcase %}
{% if delivery_instructions != blank  %}
<p><b>配達情報:</b> {{ delivery_instructions }}</p>
{% endif %}
{% if consolidated_estimated_delivery_time %}
{% if has_multiple_delivery_methods %}
<h3 class="estimated_delivery__title">配達予定</h3>
<p>{{ consolidated_estimated_delivery_time }}</p>
{% else %}
<p>
   配達予定 <b>{{ consolidated_estimated_delivery_time }}</b>
</p>
{% endif %}
{% endif %}
{% endif %}
{% endif %}